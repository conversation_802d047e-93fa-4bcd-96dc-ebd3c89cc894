import json
import decimal
from collections import defaultdict
from datetime import datetime, date, timedelta
from io import BytesIO

import pandas as pd
from django.contrib.auth.decorators import login_required
from django.views.generic import FormView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from .forms import DentistSelectionForm
from items.models import Currency
from django.core.serializers.json import DjangoJSONEncoder
from django.db.models import (
    F, Avg, Sum, Count, Min, Max, Q, Value, Case, When,
    ExpressionWrapper, DurationField, DecimalField
)
from django.db.models.functions import (
    TruncDate, TruncMonth, Concat, Coalesce
)
from django.http import (
    JsonResponse, HttpResponse, HttpResponseForbidden
)
from django.shortcuts import render, redirect, reverse, get_object_or_404
from django.utils import timezone
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.platypus import (
    SimpleDocTemplate, Table, TableStyle, PageBreak
)

from case.models import Case, CaseItem, WorkflowStage
from billing.models import Invoice, InvoiceItem
from Dentists.models import Dentist
from finance.models import Payment
from items.models import Currency, ExchangeRate, Item
from patients.models import Patient

import logging

# Configure logging
logger = logging.getLogger(__name__)

######################
# Helper Functions
######################

def parse_date(date_string):
    """
    Parse a date string in YYYY-MM-DD format to a date object.
    Returns None if the input is invalid or None.
    """
    if not date_string:
        return None
    try:
        return datetime.strptime(date_string, '%Y-%m-%d').date()
    except ValueError:
        logger.error(f"Invalid date format: {date_string}")
        return None

def convert_to_base_currency(amount, from_currency_code, to_currency_code):
    """
    Convert amount from one currency to another using exchange rates.
    If from_currency_code is the same as to_currency_code, returns amount.
    If exchange rate is not found, tries to find the inverse rate.
    If no rate is found, logs a warning and returns the original amount.
    """
    if from_currency_code == to_currency_code:
        return amount

    try:
        # Try direct conversion
        exchange_rate = ExchangeRate.objects.filter(
            from_currency__code=from_currency_code,
            to_currency__code=to_currency_code
        ).order_by('-date').first()

        if exchange_rate:
            return amount * exchange_rate.rate

        # Try inverse conversion
        inverse_exchange_rate = ExchangeRate.objects.filter(
            from_currency__code=to_currency_code,
            to_currency__code=from_currency_code
        ).order_by('-date').first()

        if inverse_exchange_rate:
            return amount / inverse_exchange_rate.rate

        # If no direct or inverse rate, log warning and return original amount
        logger.warning(f"No exchange rate found between {from_currency_code} and {to_currency_code}")
        return amount

    except Exception as e:
        logger.error(f"Error converting currency from {from_currency_code} to {to_currency_code}: {e}")
        return amount

def get_total_value_of_all_cases(base_currency_code='ALL', start_date=None, end_date=None):
    """
    Calculate the total value of all cases within a date range, converting to base currency if needed.
    """
    total_value = decimal.Decimal('0.00')
    case_items_query = CaseItem.objects.all()

    if start_date and end_date:
        case_items_query = case_items_query.filter(
            case__received_date_time__gte=start_date,
            case__received_date_time__lte=end_date
        )
    elif start_date:
        case_items_query = case_items_query.filter(
            case__received_date_time__gte=start_date
        )
    elif end_date:
        case_items_query = case_items_query.filter(
            case__received_date_time__lte=end_date
        )

    for case_item in case_items_query.select_related('item__currency'):
        item_total = case_item.quantity * case_item.item.selling_price
        item_value_in_base = convert_to_base_currency(
            item_total,
            case_item.item.currency.code,
            base_currency_code
        )
        total_value += item_value_in_base

    return total_value

def get_total_invoices_amount(base_currency_code='ALL', date_filters=None):
    """
    Calculate the total invoices amount within a date range, converting to base currency if needed.
    """
    total_invoices_amount = decimal.Decimal('0.00')
    invoices_query = Invoice.objects.all()

    if date_filters:
        invoices_query = invoices_query.filter(date_filters)

    for invoice in invoices_query.select_related('currency'):
        invoice_amount_in_base = convert_to_base_currency(
            invoice.total_amount,
            invoice.currency.code,
            base_currency_code
        )
        total_invoices_amount += invoice_amount_in_base

    return total_invoices_amount

def get_total_payments(base_currency_code='ALL', start_date=None, end_date=None):
    """
    Calculate the total payments within a date range, converting to base currency if needed.
    """
    total_payments = decimal.Decimal('0.00')
    payments_query = Payment.objects.all()

    if start_date and end_date:
        payments_query = payments_query.filter(
            date__gte=start_date,
            date__lte=end_date
        )
    elif start_date:
        payments_query = payments_query.filter(
            date__gte=start_date
        )
    elif end_date:
        payments_query = payments_query.filter(
            date__lte=end_date
        )

    for payment in payments_query.select_related('currency'):
        payment_amount_in_base = convert_to_base_currency(
            payment.amount,
            payment.currency.code,
            base_currency_code
        )
        total_payments += payment_amount_in_base

    return total_payments

######################
# View Definitions
######################

@login_required
def cycle_time_report(request):
    """
    Generates a report on cycle times for cases.
    """
    try:
        # Get date range from request
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        # Create date filters
        date_filters = Q()
        if start_date and end_date:
            date_filters &= Q(received_date_time__date__range=[start_date, end_date])
        elif start_date:
            date_filters &= Q(received_date_time__date__gte=start_date)
        elif end_date:
            date_filters &= Q(received_date_time__date__lte=end_date)

        # Only include cases that have been completed
        date_filters &= Q(actual_completion__isnull=False)

        # Annotate cases with duration
        cases = Case.objects.filter(date_filters).annotate(
            duration=ExpressionWrapper(
                F('actual_completion') - F('received_date_time'),
                output_field=DurationField()
            )
        ).order_by('duration')

        # Calculate total and average duration
        total_cases = cases.count()
        avg_duration = cases.aggregate(avg=Avg('duration'))['avg']

        # Calculate median duration (middle value)
        if total_cases > 0:
            median_index = total_cases // 2
            median_duration = list(cases.values_list('duration', flat=True))[median_index]
        else:
            median_duration = None

        # Calculate min and max durations
        min_duration = cases.aggregate(min=Min('duration'))['min']
        max_duration = cases.aggregate(max=Max('duration'))['max']

        # Group cases by duration ranges
        duration_ranges = [
            {'name': 'Less than 1 day', 'count': 0, 'min_hours': 0, 'max_hours': 24},
            {'name': '1-2 days', 'count': 0, 'min_hours': 24, 'max_hours': 48},
            {'name': '2-3 days', 'count': 0, 'min_hours': 48, 'max_hours': 72},
            {'name': '3-5 days', 'count': 0, 'min_hours': 72, 'max_hours': 120},
            {'name': '5-7 days', 'count': 0, 'min_hours': 120, 'max_hours': 168},
            {'name': '1-2 weeks', 'count': 0, 'min_hours': 168, 'max_hours': 336},
            {'name': 'More than 2 weeks', 'count': 0, 'min_hours': 336, 'max_hours': float('inf')}
        ]

        for case in cases:
            if case.duration:
                duration_hours = case.duration.total_seconds() / 3600
                for range_data in duration_ranges:
                    if range_data['min_hours'] <= duration_hours < range_data['max_hours']:
                        range_data['count'] += 1
                        break

        # Calculate percentages for each range
        if total_cases > 0:
            for range_data in duration_ranges:
                range_data['percentage'] = (range_data['count'] / total_cases) * 100
        else:
            for range_data in duration_ranges:
                range_data['percentage'] = 0

        # Group cases by dentist
        dentist_data = cases.values('dentist__id', 'dentist__first_name', 'dentist__last_name').annotate(
            case_count=Count('case_number'),
            avg_duration=Avg('duration')
        ).order_by('-case_count')

        # Add full name to dentist data
        for dentist in dentist_data:
            dentist['full_name'] = f"{dentist['dentist__first_name']} {dentist['dentist__last_name']}"

        # Format durations for display
        formatted_cases = []
        for case in cases:
            # Calculate hours from seconds for better display
            if case.duration:
                total_seconds = case.duration.total_seconds()
                hours = total_seconds // 3600
                remaining_seconds = total_seconds % 3600
                minutes = remaining_seconds // 60
                seconds = remaining_seconds % 60
            else:
                hours = 0
                minutes = 0
                seconds = 0

            formatted_case = {
                'case_number': case.case_number,
                'dentist_name': f"{case.dentist.first_name} {case.dentist.last_name}" if case.dentist else "N/A",
                'received_date': case.received_date_time,
                'completion_date': case.actual_completion,
                'duration': case.duration,
                'duration_days': case.duration.days if case.duration else 0,
                'duration_hours': hours,
                'duration_minutes': minutes,
                'duration_seconds': seconds,
                'duration_total_hours': (case.duration.total_seconds() / 3600) if case.duration else 0,
                'status': case.status
            }
            formatted_cases.append(formatted_case)

        context = {
            'cases': formatted_cases,
            'total_cases': total_cases,
            'avg_duration': avg_duration,
            'median_duration': median_duration,
            'min_duration': min_duration,
            'max_duration': max_duration,
            'duration_ranges': duration_ranges,
            'dentist_data': dentist_data,
            'start_date': start_date,
            'end_date': end_date
        }

        return render(request, 'reports/cycle_times.html', context)
    except Exception as e:
        logger.error(f"Error in cycle_time_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the cycle time report.'})

@login_required
def revenue_report(request):
    """
    Generates a revenue report with optional currency conversion.
    """
    try:
        # Get date range from request
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        # Fetch all currencies
        all_currencies = Currency.objects.all()

        # Get currency IDs from GET parameters
        currency_id = request.GET.get('currency_id')
        convert_to_currency_id = request.GET.get('convert_to_currency_id')

        # Get the source and target currencies if IDs are provided
        from_currency = Currency.objects.filter(id=currency_id).first() if currency_id else None
        to_currency = Currency.objects.filter(id=convert_to_currency_id).first() if convert_to_currency_id else None

        # Create date filters
        date_filters = Q()
        if start_date and end_date:
            date_filters &= Q(date__range=[start_date, end_date])
        elif start_date:
            date_filters &= Q(date__gte=start_date)
        elif end_date:
            date_filters &= Q(date__lte=end_date)

        # Fetch all paid invoices with date filter
        invoices = Invoice.objects.filter(status='PAID').filter(date_filters)

        # Filter invoices by the selected currency if provided
        if from_currency:
            invoices = invoices.filter(currency=from_currency)

        # Get total invoice count
        total_invoices = invoices.count()

        # Get unique dentist count
        unique_dentists = invoices.values('dentist').distinct().count()

        # Calculate the total revenue from the filtered invoices and convert if needed
        total_revenue = decimal.Decimal('0.00')
        invoice_data = []

        # Group invoices by month
        monthly_revenue = defaultdict(decimal.Decimal)

        for invoice in invoices.select_related('currency', 'dentist'):
            target_currency_code = (to_currency.code if to_currency else
                                     (from_currency.code if from_currency else 'ALL'))

            # Convert invoice amount to target currency
            converted_amount = convert_to_base_currency(
                invoice.total_amount,
                invoice.currency.code,
                target_currency_code
            )

            total_revenue += converted_amount

            # Add to monthly data
            month_key = invoice.date.strftime('%Y-%m')
            monthly_revenue[month_key] += converted_amount

            # Add to invoice data for table
            invoice_data.append({
                'id': invoice.id,
                'number': invoice.invoice_number,
                'date': invoice.date,
                'dentist': invoice.dentist.first_name + ' ' + invoice.dentist.last_name if invoice.dentist else 'N/A',
                'original_amount': invoice.total_amount,
                'original_currency': invoice.currency.code,
                'converted_amount': converted_amount,
                'target_currency': target_currency_code
            })

        # Convert total revenue if a target currency is specified
        if from_currency and to_currency:
            total_revenue = convert_to_base_currency(
                total_revenue,
                from_currency.code,
                to_currency.code
            )

        # Calculate average invoice value
        avg_invoice_value = total_revenue / total_invoices if total_invoices > 0 else decimal.Decimal('0.00')

        # Format monthly data for chart
        monthly_data = []
        for month, amount in sorted(monthly_revenue.items()):
            monthly_data.append({
                'month': month,
                'amount': float(amount)
            })

        # Get top dentists by revenue
        dentist_revenue = defaultdict(decimal.Decimal)
        for invoice in invoice_data:
            dentist_revenue[invoice['dentist']] += invoice['converted_amount']

        top_dentists = []
        for dentist, amount in sorted(dentist_revenue.items(), key=lambda x: x[1], reverse=True)[:5]:
            top_dentists.append({
                'name': dentist,
                'amount': amount,
                'percentage': (amount / total_revenue * 100) if total_revenue > 0 else 0
            })

        # Build the context for the template
        context = {
            'all_currencies': all_currencies,
            'from_currency_id': from_currency.id if from_currency else None,
            'to_currency_id': to_currency.id if to_currency else None,
            'total_revenue': total_revenue,
            'from_currency': from_currency.name if from_currency else None,
            'to_currency': to_currency.name if to_currency else None,
            'target_currency_code': to_currency.code if to_currency else (from_currency.code if from_currency else 'ALL'),
            'total_invoices': total_invoices,
            'unique_dentists': unique_dentists,
            'avg_invoice_value': avg_invoice_value,
            'invoice_data': invoice_data,
            'monthly_data': monthly_data,
            'top_dentists': top_dentists,
            'start_date': start_date,
            'end_date': end_date
        }

        return render(request, 'reports/revenue.html', context)
    except Exception as e:
        logger.error(f"Error in revenue_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the revenue report.'})

@login_required
def reports_dashboard(request):
    """
    Enhanced Executive Dashboard with real-time KPIs and comprehensive metrics.
    """
    try:
        # Get date range from request or default to last 30 days
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        # Previous period for comparison
        days_in_period = (end_date - start_date).days + 1
        previous_start = start_date - timedelta(days=days_in_period)
        previous_end = start_date - timedelta(days=1)

        # === FINANCIAL KPIs ===
        current_revenue = get_total_invoices_amount('ALL', Q(date__range=[start_date, end_date]))
        previous_revenue = get_total_invoices_amount('ALL', Q(date__range=[previous_start, previous_end]))
        revenue_change = ((current_revenue - previous_revenue) / previous_revenue * 100) if previous_revenue > 0 else 0

        current_payments = get_total_payments('ALL', start_date, end_date)
        previous_payments = get_total_payments('ALL', previous_start, previous_end)
        payments_change = ((current_payments - previous_payments) / previous_payments * 100) if previous_payments > 0 else 0

        outstanding_balance = current_revenue - current_payments

        # === OPERATIONAL KPIs ===
        current_cases = Case.objects.filter(received_date_time__date__range=[start_date, end_date])
        previous_cases = Case.objects.filter(received_date_time__date__range=[previous_start, previous_end])

        current_case_count = current_cases.count()
        previous_case_count = previous_cases.count()
        case_volume_change = ((current_case_count - previous_case_count) / previous_case_count * 100) if previous_case_count > 0 else 0

        # Completion rate
        completed_current = current_cases.filter(status='completed').count()
        completion_rate = (completed_current / current_case_count * 100) if current_case_count > 0 else 0

        completed_previous = previous_cases.filter(status='completed').count()
        previous_completion_rate = (completed_previous / previous_case_count * 100) if previous_case_count > 0 else 0
        completion_rate_change = completion_rate - previous_completion_rate

        # Average cycle time
        completed_cases_with_duration = current_cases.filter(
            status='completed',
            actual_completion__isnull=False
        ).annotate(
            duration=ExpressionWrapper(
                F('actual_completion') - F('received_date_time'),
                output_field=DurationField()
            )
        )

        avg_cycle_time = completed_cases_with_duration.aggregate(avg=Avg('duration'))['avg']
        avg_cycle_days = avg_cycle_time.days if avg_cycle_time else 0

        # === QUALITY KPIs ===
        avg_patient_feedback = current_cases.aggregate(avg=Avg('patient_feedback'))['avg'] or 0
        avg_dentist_feedback = current_cases.aggregate(avg=Avg('dentist_feedback'))['avg'] or 0
        total_revisions = current_cases.aggregate(total=Sum('revision_count'))['total'] or 0
        revision_rate = (total_revisions / current_case_count * 100) if current_case_count > 0 else 0

        # === INVENTORY KPIs ===
        total_inventory_value = sum(item.stock_quantity * (item.selling_price or 0) for item in Item.objects.all())
        low_stock_items = Item.objects.filter(stock_quantity__lte=F('minimum_stock_level')).count()
        total_items = Item.objects.count()

        # === TOP PERFORMERS ===
        top_dentists = Invoice.objects.filter(date__range=[start_date, end_date]).values(
            'dentist__first_name', 'dentist__last_name'
        ).annotate(
            total_revenue=Sum('total_amount'),
            case_count=Count('case')
        ).order_by('-total_revenue')[:5]

        # Format dentist names
        for dentist in top_dentists:
            dentist['full_name'] = f"{dentist['dentist__first_name']} {dentist['dentist__last_name']}"

        # === RECENT ACTIVITIES ===
        recent_cases = Case.objects.select_related('dentist', 'patient').order_by('-received_date_time')[:10]
        overdue_cases = Case.objects.filter(
            status='in_progress',
            ship_date_time__lt=timezone.now().date()
        ).count()

        # === MONTHLY TRENDS ===
        monthly_revenue = Invoice.objects.filter(
            date__gte=start_date - timedelta(days=365)
        ).annotate(
            month=TruncMonth('date')
        ).values('month').annotate(
            revenue=Sum('total_amount'),
            case_count=Count('case')
        ).order_by('month')

        context = {
            'start_date': start_date,
            'end_date': end_date,
            'days_in_period': days_in_period,

            # Financial KPIs
            'current_revenue': current_revenue,
            'revenue_change': revenue_change,
            'current_payments': current_payments,
            'payments_change': payments_change,
            'outstanding_balance': outstanding_balance,
            'payment_rate': (current_payments / current_revenue * 100) if current_revenue > 0 else 0,

            # Operational KPIs
            'current_case_count': current_case_count,
            'case_volume_change': case_volume_change,
            'completion_rate': completion_rate,
            'completion_rate_change': completion_rate_change,
            'avg_cycle_days': avg_cycle_days,
            'overdue_cases': overdue_cases,

            # Quality KPIs
            'avg_patient_feedback': avg_patient_feedback,
            'avg_dentist_feedback': avg_dentist_feedback,
            'revision_rate': revision_rate,

            # Inventory KPIs
            'total_inventory_value': total_inventory_value,
            'low_stock_items': low_stock_items,
            'total_items': total_items,
            'stock_health': ((total_items - low_stock_items) / total_items * 100) if total_items > 0 else 0,

            # Top performers and activities
            'top_dentists': top_dentists,
            'recent_cases': recent_cases,
            'monthly_trends': list(monthly_revenue),
        }

        return render(request, 'reports/enhanced_dashboard.html', context)
    except Exception as e:
        logger.error(f"Error in enhanced reports_dashboard: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while loading the enhanced dashboard.'})

@login_required
def item_inventory_report(request):
    """
    Generates a detailed inventory report for items, showing which dentists have purchased which items,
    quantities purchased, and allowing sorting by various criteria.
    """
    try:
        # Get date range from request
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=365))  # Default to last year
        end_date = parse_date(end_date_str) or date.today()

        # Get sorting parameters
        sort_by = request.GET.get('sort_by', 'item_name')  # Default sort by item name
        sort_order = request.GET.get('sort_order', 'asc')  # Default ascending order

        # Get filter parameters
        dentist_id = request.GET.get('dentist_id')
        item_id = request.GET.get('item_id')
        min_quantity = request.GET.get('min_quantity')

        # Create date filters
        date_filters = Q()
        if start_date and end_date:
            date_filters &= Q(case__received_date_time__date__range=[start_date, end_date])
        elif start_date:
            date_filters &= Q(case__received_date_time__date__gte=start_date)
        elif end_date:
            date_filters &= Q(case__received_date_time__date__lte=end_date)

        # Base query for case items
        case_items_query = CaseItem.objects.filter(date_filters)

        # Apply filters if provided
        if dentist_id:
            case_items_query = case_items_query.filter(case__dentist_id=dentist_id)
        if item_id:
            case_items_query = case_items_query.filter(item_id=item_id)
        if min_quantity and min_quantity.isdigit():
            min_quantity = int(min_quantity)
        else:
            min_quantity = None

        # Get all items
        items = Item.objects.all()

        # Get all dentists
        dentists = Dentist.objects.all()

        # Initialize data structure for item inventory report
        item_inventory_data = []

        # Calculate total quantities and revenue
        total_items_sold = 0
        total_revenue = decimal.Decimal('0.00')

        # Process each item
        for item in items:
            # Get case items for this item
            item_case_items = case_items_query.filter(item=item)

            # Skip items with no usage in the period if min_quantity is set
            total_quantity = item_case_items.aggregate(total=Sum('quantity'))['total'] or 0
            if min_quantity and total_quantity < min_quantity:
                continue

            # Skip items with no usage in the period
            if total_quantity == 0:
                continue

            total_items_sold += total_quantity

            # Calculate total revenue for this item
            item_revenue = total_quantity * item.selling_price
            total_revenue += item_revenue

            # Get usage by dentist
            dentist_usage = item_case_items.values(
                'case__dentist__id',
                'case__dentist__first_name',
                'case__dentist__last_name'
            ).annotate(
                quantity_sum=Sum('quantity')
            ).order_by('-quantity_sum')

            # Convert dentist usage to serializable format
            dentist_usage_serializable = []
            for dentist in dentist_usage:
                dentist_full_name = f"{dentist['case__dentist__first_name']} {dentist['case__dentist__last_name']}"
                dentist_usage_serializable.append({
                    'dentist_id': dentist['case__dentist__id'],
                    'dentist_name': dentist_full_name,
                    'quantity': dentist['quantity_sum'],
                    'percentage': (dentist['quantity_sum'] / total_quantity * 100) if total_quantity > 0 else 0
                })

            # Get monthly usage
            monthly_usage = item_case_items.annotate(
                month=TruncMonth('case__received_date_time')
            ).values('month').annotate(
                quantity=Sum('quantity')
            ).order_by('month')

            # Convert monthly usage to serializable format
            monthly_usage_serializable = []
            for month_data in monthly_usage:
                monthly_usage_serializable.append({
                    'month': month_data['month'].strftime('%Y-%m'),
                    'quantity': month_data['quantity']
                })

            # Create item details
            item_detail = {
                'item_id': item.id,
                'item_name': item.name,
                'item_description': item.description or '',
                'total_quantity': total_quantity,
                'unit_price': item.selling_price,
                'currency': item.currency.code,
                'total_revenue': item_revenue,
                'dentist_usage': dentist_usage_serializable,
                'monthly_usage': monthly_usage_serializable,
                'top_dentist': dentist_usage_serializable[0]['dentist_name'] if dentist_usage_serializable else 'N/A',
                'top_dentist_quantity': dentist_usage_serializable[0]['quantity'] if dentist_usage_serializable else 0,
                'top_dentist_percentage': dentist_usage_serializable[0]['percentage'] if dentist_usage_serializable else 0,
            }

            item_inventory_data.append(item_detail)

        # Sort the data based on sort parameters
        if sort_by == 'item_name':
            item_inventory_data.sort(key=lambda x: x['item_name'], reverse=(sort_order == 'desc'))
        elif sort_by == 'total_quantity':
            item_inventory_data.sort(key=lambda x: x['total_quantity'], reverse=(sort_order == 'desc'))
        elif sort_by == 'total_revenue':
            item_inventory_data.sort(key=lambda x: x['total_revenue'], reverse=(sort_order == 'desc'))
        elif sort_by == 'top_dentist':
            item_inventory_data.sort(key=lambda x: x['top_dentist'], reverse=(sort_order == 'desc'))

        # Calculate average revenue per item
        avg_revenue_per_item = total_revenue / len(item_inventory_data) if item_inventory_data else decimal.Decimal('0.00')

        # Get top items by quantity
        top_items_by_quantity = sorted(item_inventory_data, key=lambda x: x['total_quantity'], reverse=True)[:5]

        # Get top items by revenue
        top_items_by_revenue = sorted(item_inventory_data, key=lambda x: x['total_revenue'], reverse=True)[:5]

        # Get top dentists by total purchases
        dentist_purchases = {}
        for item in item_inventory_data:
            for dentist in item['dentist_usage']:
                dentist_id = dentist['dentist_id']
                if dentist_id in dentist_purchases:
                    dentist_purchases[dentist_id]['total_quantity'] += dentist['quantity']
                    dentist_purchases[dentist_id]['total_revenue'] += (dentist['quantity'] * item['unit_price'])
                else:
                    dentist_purchases[dentist_id] = {
                        'dentist_id': dentist_id,
                        'dentist_name': dentist['dentist_name'],
                        'total_quantity': dentist['quantity'],
                        'total_revenue': (dentist['quantity'] * item['unit_price'])
                    }

        top_dentists = sorted(dentist_purchases.values(), key=lambda x: x['total_revenue'], reverse=True)[:5]

        # Build the context for the template
        context = {
            'item_inventory_data': item_inventory_data,
            'total_items_sold': total_items_sold,
            'total_revenue': total_revenue,
            'avg_revenue_per_item': avg_revenue_per_item,
            'top_items_by_quantity': top_items_by_quantity,
            'top_items_by_revenue': top_items_by_revenue,
            'top_dentists': top_dentists,
            'dentists': dentists,
            'items': items,
            'start_date': start_date,
            'end_date': end_date,
            'sort_by': sort_by,
            'sort_order': sort_order,
            'dentist_id': dentist_id,
            'item_id': item_id,
            'min_quantity': min_quantity
        }

        return render(request, 'reports/item_inventory_report.html', context)
    except Exception as e:
        logger.error(f"Error in item_inventory_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the item inventory report.'})

@login_required
def dentist_financial_report(request):
    """
    Generates a financial report for each dentist, showing revenue, payments, and outstanding balances.
    """
    try:
        # Get date range from request
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        # Get dentist ID from request or show all dentists
        dentist_id = request.GET.get('dentist_id')

        # Get all dentists for the dropdown
        all_dentists = Dentist.objects.all().order_by('first_name', 'last_name')

        if dentist_id:
            # Get specific dentist
            dentists = Dentist.objects.filter(id=dentist_id)
        else:
            # Get all dentists
            dentists = all_dentists

        # Initialize data structure for dentist financial data
        dentist_financials = []

        # Calculate total revenue across all dentists for percentage calculations
        total_revenue = decimal.Decimal('0.00')
        total_paid = decimal.Decimal('0.00')
        total_outstanding = decimal.Decimal('0.00')

        # Get financial data for each dentist
        for dentist in dentists:
            # Get invoices for this dentist in the date range
            invoices = Invoice.objects.filter(
                dentist=dentist,
                date__range=[start_date, end_date]
            )

            # Skip dentists with no invoices in the period
            if not invoices.exists():
                continue

            # Calculate total revenue for this dentist
            dentist_revenue = invoices.aggregate(total=Sum('total_amount'))['total'] or decimal.Decimal('0.00')

            # Calculate paid amount
            paid_invoices = invoices.filter(status='paid')
            paid_amount = paid_invoices.aggregate(total=Sum('total_amount'))['total'] or decimal.Decimal('0.00')

            # Calculate partially paid amount
            partial_invoices = invoices.filter(status='partial')
            partial_amount = decimal.Decimal('0.00')
            for invoice in partial_invoices:
                # Sum payments for this invoice
                payments = invoice.billing_invoice_payments.aggregate(total=Sum('amount'))['total'] or decimal.Decimal('0.00')
                partial_amount += payments

            # Total paid is sum of fully paid invoices plus partial payments
            total_paid_amount = paid_amount + partial_amount

            # Outstanding amount is total revenue minus total paid
            outstanding_amount = dentist_revenue - total_paid_amount

            # Calculate payment rate
            payment_rate = (total_paid_amount / dentist_revenue * 100) if dentist_revenue > 0 else 0

            # Add to totals
            total_revenue += dentist_revenue
            total_paid += total_paid_amount
            total_outstanding += outstanding_amount

            # Get case count
            case_count = Case.objects.filter(
                dentist=dentist,
                received_date_time__date__range=[start_date, end_date]
            ).count()

            # Calculate average revenue per case
            avg_revenue_per_case = dentist_revenue / case_count if case_count > 0 else 0

            # Add dentist data to the list
            dentist_financials.append({
                'id': dentist.id,
                'name': f"{dentist.first_name} {dentist.last_name}",
                'revenue': dentist_revenue,
                'paid': total_paid_amount,
                'outstanding': outstanding_amount,
                'payment_rate': payment_rate,
                'case_count': case_count,
                'avg_revenue_per_case': avg_revenue_per_case,
                'invoice_count': invoices.count()
            })

        # Sort dentists by revenue (highest first)
        dentist_financials.sort(key=lambda x: x['revenue'], reverse=True)

        # Calculate percentages of total revenue
        for dentist in dentist_financials:
            dentist['revenue_percentage'] = (dentist['revenue'] / total_revenue * 100) if total_revenue > 0 else 0

        # Calculate overall payment rate
        overall_payment_rate = (total_paid / total_revenue * 100) if total_revenue > 0 else 0

        # If a specific dentist is selected, get detailed case and invoice information
        detailed_data = None
        if dentist_id and dentists.exists():
            selected_dentist = dentists.first()

            # Get all cases for this dentist in the date range
            cases = Case.objects.filter(
                dentist=selected_dentist,
                received_date_time__date__range=[start_date, end_date]
            ).select_related('patient').order_by('-received_date_time')

            # Get all invoices for this dentist in the date range
            invoices = Invoice.objects.filter(
                dentist=selected_dentist,
                date__range=[start_date, end_date]
            ).select_related('case', 'currency').order_by('-date')

            # For each invoice, get payment information
            invoice_data = []
            for invoice in invoices:
                # Get payments for this invoice
                payments = invoice.finance_invoice_payments.all()
                total_paid = sum(payment.amount for payment in payments)
                outstanding = invoice.total_amount - total_paid

                invoice_data.append({
                    'id': invoice.id,
                    'date': invoice.date,
                    'case_number': invoice.case.case_number if invoice.case else 'N/A',
                    'patient_name': invoice.patient_name,
                    'total_amount': invoice.total_amount,
                    'currency': invoice.currency.code,
                    'status': invoice.get_status_display(),
                    'total_paid': total_paid,
                    'outstanding': outstanding,
                    'payments': payments
                })

            # Prepare detailed data
            detailed_data = {
                'dentist': selected_dentist,
                'cases': cases,
                'invoices': invoice_data,
                'case_count': cases.count(),
                'invoice_count': len(invoice_data)
            }

        context = {
            'dentist_financials': dentist_financials,
            'start_date': start_date,
            'end_date': end_date,
            'total_revenue': total_revenue,
            'total_paid': total_paid,
            'total_outstanding': total_outstanding,
            'overall_payment_rate': overall_payment_rate,
            'total_dentists': len(dentist_financials),
            'all_dentists': all_dentists,
            'selected_dentist_id': dentist_id,
            'detailed_data': detailed_data
        }

        return render(request, 'reports/dentist_financial_report.html', context)
    except Exception as e:
        logger.error(f"Error in dentist_financial_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the dentist financial report.'})

@login_required
def case_volume_report(request):
    """
    Generates a report on case volumes, including opened and closed cases per date.
    """
    try:
        # Get date range from request
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        # Create date filters
        date_filters = Q()
        if start_date and end_date:
            date_filters &= Q(received_date_time__date__range=[start_date, end_date]) | \
                          Q(actual_completion__date__range=[start_date, end_date])

        # Annotate cases with opening and closing dates
        cases = Case.objects.filter(date_filters).annotate(
            opening_date=TruncDate('received_date_time'),
            closing_date=TruncDate('actual_completion')
        ).values('opening_date', 'closing_date')

        # Initialize data structure to hold counts
        data = defaultdict(lambda: {'opened': 0, 'closed': 0})

        # Ensure all dates in the range are included
        current_date = start_date
        while current_date <= end_date:
            data[current_date] = {'opened': 0, 'closed': 0}
            current_date += timedelta(days=1)

        # Count cases opened and closed for each date
        for case in cases:
            if case['opening_date'] and start_date <= case['opening_date'] <= end_date:
                data[case['opening_date']]['opened'] += 1
            if case['closing_date'] and start_date <= case['closing_date'] <= end_date:
                data[case['closing_date']]['closed'] += 1

        # Convert data to a format suitable for the template
        context_data = [
            {
                'date': date.strftime('%Y-%m-%d'),
                'opened': counts['opened'],
                'closed': counts['closed'],
                'net': counts['opened'] - counts['closed']
            }
            for date, counts in sorted(data.items())
        ]

        # Calculate summary metrics
        total_opened = sum(item['opened'] for item in context_data)
        total_closed = sum(item['closed'] for item in context_data)
        net_change = total_opened - total_closed

        # Calculate daily averages
        days_in_period = (end_date - start_date).days + 1
        avg_daily_volume = round((total_opened + total_closed) / (2 * days_in_period), 1) if days_in_period > 0 else 0

        # Calculate change from previous period
        previous_start = start_date - timedelta(days=days_in_period)
        previous_end = start_date - timedelta(days=1)

        previous_cases = Case.objects.filter(
            Q(received_date_time__date__range=[previous_start, previous_end]) | \
            Q(actual_completion__date__range=[previous_start, previous_end])
        ).annotate(
            opening_date=TruncDate('received_date_time'),
            closing_date=TruncDate('actual_completion')
        ).values('opening_date', 'closing_date')

        previous_opened = 0
        previous_closed = 0

        for case in previous_cases:
            if case['opening_date'] and previous_start <= case['opening_date'] <= previous_end:
                previous_opened += 1
            if case['closing_date'] and previous_start <= case['closing_date'] <= previous_end:
                previous_closed += 1

        # Calculate percentage changes
        opened_change = round(((total_opened - previous_opened) / previous_opened) * 100, 1) if previous_opened > 0 else 0
        closed_change = round(((total_closed - previous_closed) / previous_closed) * 100, 1) if previous_closed > 0 else 0

        context = {
            'cases': context_data,
            'total_opened': total_opened,
            'total_closed': total_closed,
            'net_change': net_change,
            'avg_daily_volume': avg_daily_volume,
            'opened_change': opened_change,
            'closed_change': closed_change,
            'start_date': start_date,
            'end_date': end_date
        }

        return render(request, 'reports/case_volume_report.html', context)
    except Exception as e:
        logger.error(f"Error in case_volume_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the case volume report.'})

@login_required
def case_status_report(request):
    """
    Generates a report on case statuses within a specified date range.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        filters = Q()
        if start_date and end_date:
            filters &= Q(received_date_time__date__range=[start_date, end_date])
        elif start_date:
            filters &= Q(received_date_time__date__gte=start_date)
        elif end_date:
            filters &= Q(received_date_time__date__lte=end_date)

        # Get status counts
        status_data = list(Case.objects.filter(filters).values('status').annotate(
            total=Count('case_number')
        ).order_by('-total'))

        # Calculate total cases for percentage calculation
        total_cases = sum(item['total'] for item in status_data)

        # Calculate percentage for each status
        if total_cases > 0:
            for item in status_data:
                item['percentage'] = (item['total'] / total_cases) * 100
        else:
            for item in status_data:
                item['percentage'] = 0

        context = {
            'status_data': status_data,
            'start_date': start_date,
            'end_date': end_date,
            'total_cases': total_cases
        }

        return render(request, 'reports/case_status_report.html', context)
    except Exception as e:
        logger.error(f"Error in case_status_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the case status report.'})

@login_required
def dentist_leaderboard(request):
    """
    Generates a leaderboard ranking dentists based on the number of cases handled.
    Supports AJAX requests for dynamic updates.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        filters = Q()
        if start_date and end_date:
            filters &= Q(received_date_time__date__range=[start_date, end_date])
        elif start_date:
            filters &= Q(received_date_time__date__gte=start_date)
        elif end_date:
            filters &= Q(received_date_time__date__lte=end_date)

        cases = Case.objects.filter(filters)
        total_cases = cases.count()

        # Get top dentists by case count
        dentists = list(cases.values('dentist__id', 'dentist__first_name', 'dentist__last_name')
        .annotate(
            full_name=Concat('dentist__first_name', Value(' '), 'dentist__last_name'),
            total_cases=Count('case_number'),
            completed_cases=Count('case_number', filter=Q(status='completed')),
            in_progress_cases=Count('case_number', filter=Q(status='in_progress')),
            on_hold_cases=Count('case_number', filter=Q(status='on_hold'))
        ).order_by('-total_cases')[:10])

        # Calculate percentages
        for dentist in dentists:
            if dentist['total_cases'] > 0:
                dentist['completion_rate'] = (dentist['completed_cases'] / dentist['total_cases']) * 100
                dentist['percentage_of_total'] = (dentist['total_cases'] / total_cases) * 100 if total_cases > 0 else 0
            else:
                dentist['completion_rate'] = 0
                dentist['percentage_of_total'] = 0

        # Get previous period data for comparison
        days_in_period = (end_date - start_date).days + 1
        previous_start = start_date - timedelta(days=days_in_period)
        previous_end = start_date - timedelta(days=1)

        previous_filters = Q(received_date_time__date__range=[previous_start, previous_end])
        previous_cases = Case.objects.filter(previous_filters)

        # Get previous period dentist data
        previous_dentists = dict(previous_cases.annotate(
            full_name=Concat('dentist__first_name', Value(' '), 'dentist__last_name')
        ).values('full_name').annotate(
            total_cases=Count('case_number')
        ).values_list('full_name', 'total_cases'))

        # Calculate change from previous period
        for dentist in dentists:
            previous_count = previous_dentists.get(dentist['full_name'], 0)
            if previous_count > 0:
                dentist['change_percentage'] = ((dentist['total_cases'] - previous_count) / previous_count) * 100
            else:
                dentist['change_percentage'] = 100 if dentist['total_cases'] > 0 else 0

        logger.info(f"Dentist leaderboard from {start_date} to {end_date}: {dentists}")

        if request.GET.get('ajax') == 'true':
            return JsonResponse(dentists, safe=False)

        context = {
            'dentists': dentists,
            'start_date': start_date,
            'end_date': end_date,
            'total_cases': total_cases,
            'total_dentists': len(set(cases.values_list('dentist__id', flat=True))),
            'days_in_period': days_in_period
        }
        return render(request, 'reports/dentist_leaderboard.html', context)
    except Exception as e:
        logger.error(f"Error in dentist_leaderboard: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the dentist leaderboard.'})

@login_required
def appointment_scheduling_report(request):
    """
    Generates a report on appointment scheduling, including case statuses and overdue cases.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        date_filters = Q()
        if start_date and end_date:
            date_filters &= Q(received_date_time__gte=start_date, received_date_time__lte=end_date)
        elif start_date:
            date_filters &= Q(received_date_time__gte=start_date)
        elif end_date:
            date_filters &= Q(received_date_time__lte=end_date)

        cases = Case.objects.filter(date_filters)

        # Case status counts
        status_counts = cases.values('status').annotate(count=Count('case_number')).order_by('-count')

        # Average time spent in each stage
        avg_time_per_stage = cases.annotate(
            stage_duration=ExpressionWrapper(
                F('actual_completion') - F('received_date_time'),
                output_field=DurationField()
            )
        ).values('current_stage__name').annotate(
            avg_duration=Avg('stage_duration')
        ).order_by('current_stage__name')

        # Overdue cases with days overdue calculation
        today = date.today()
        overdue_cases = cases.filter(status='in_progress', ship_date_time__lt=today).annotate(
            overdue_days=ExpressionWrapper(
                timezone.now().date() - F('ship_date_time__date'),
                output_field=DurationField()
            )
        )

        # Convert overdue_days to integer days
        overdue_cases_list = []
        for case in overdue_cases:
            if isinstance(case.overdue_days, timedelta):
                days_overdue = case.overdue_days.days
            else:
                days_overdue = 0
            overdue_cases_list.append({
                'case_number': case.case_number,
                'patient': case.patient,
                'dentist': f"{case.dentist.first_name} {case.dentist.last_name}" if case.dentist else "N/A",
                'received_date_time': case.received_date_time,
                'ship_date_time': case.ship_date_time,
                'overdue_days': days_overdue,
            })

        # In-progress and on-hold cases
        in_progress_cases = cases.filter(status='in_progress', ship_date_time__gte=today)
        on_hold_cases = cases.filter(status='on_hold')

        context = {
            'status_counts': status_counts,
            'avg_time_per_stage': avg_time_per_stage,
            'overdue_cases': overdue_cases_list,
            'in_progress_cases': in_progress_cases,
            'on_hold_cases': on_hold_cases,
            'start_date': start_date,
            'end_date': end_date,
        }

        return render(request, 'reports/appointment_scheduling_report.html', context)
    except Exception as e:
        logger.error(f"Error in appointment_scheduling_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the appointment scheduling report.'})

@login_required
def export_to_excel(request):
    """
    Exports case data to an Excel file.
    """
    try:
        # Define fields to export
        fields = ['case_number', 'received_date_time', 'actual_completion', 'status', 'dentist__first_name', 'dentist__last_name']

        # Fetch the data from the Case model
        cases_data = Case.objects.select_related('dentist').all().values(*fields)

        # Convert QuerySet to a list of dictionaries
        cases_list = list(cases_data)

        if not cases_list:
            return render(request, 'reports/error.html', {'error': 'No case data available to export.'})

        # Convert timezone-aware datetimes to string format for Excel
        for case in cases_list:
            for field, value in case.items():
                if isinstance(value, datetime) and value.tzinfo is not None:
                    case[field] = timezone.localtime(value).strftime('%Y-%m-%d %H:%M:%S')

        # Convert list of dictionaries to a DataFrame
        df = pd.DataFrame(cases_list)
        if 'dentist__first_name' in df.columns and 'dentist__last_name' in df.columns:
            df['dentist_full_name'] = df['dentist__first_name'] + ' ' + df['dentist__last_name']
            df = df.drop(['dentist__first_name', 'dentist__last_name'], axis=1)

        # Create an Excel writer object and convert the DataFrame to an Excel file
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename="cases.xlsx"'

        with pd.ExcelWriter(response, engine='openpyxl') as writer:
            df.to_excel(writer, index=False)

        return response
    except Exception as e:
        logger.error(f"Error in export_to_excel: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while exporting data to Excel.'})

@login_required
def export_to_pdf(request):
    """
    Exports case data to a PDF file.
    """
    try:
        # Define fields to export
        fields = ['case_number', 'received_date_time', 'actual_completion', 'status', 'dentist__first_name', 'dentist__last_name']

        # Fetch the data from the Case model
        cases_data = Case.objects.select_related('dentist').all().values(*fields)

        # Convert QuerySet to a list of dictionaries
        cases_list = list(cases_data)

        if not cases_list:
            return render(request, 'reports/error.html', {'error': 'No case data available to export.'})

        # Create a file-like buffer to receive PDF data
        buffer = BytesIO()

        # Create the PDF object, using the buffer as its "file"
        doc = SimpleDocTemplate(buffer, pagesize=A4)

        # Prepare data for the table
        headers = ['Case Number', 'Received Date Time', 'Finished Date Time', 'Status', 'Dentist Full Name']
        data = [headers]  # column headers
        for case in cases_list:
            dentist_full_name = f"{case['dentist__first_name']} {case['dentist__last_name']}"
            received_dt = timezone.localtime(case['received_date_time']).strftime('%Y-%m-%d %H:%M:%S') if case['received_date_time'] else ''
            finished_dt = timezone.localtime(case['actual_completion']).strftime('%Y-%m-%d %H:%M:%S') if case['actual_completion'] else ''
            row = [
                case['case_number'],
                received_dt,
                finished_dt,
                case['status'],
                dentist_full_name
            ]
            data.append(row)

        # Create a table with the data and add some styling
        table = Table(data, repeatRows=1)
        style = TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),

            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),

            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0,0), (-1,-1), 1, colors.black)
        ])
        table.setStyle(style)

        # Add the table to the elements to be added to the PDF
        elements = [table]

        # Build the PDF
        doc.build(elements)

        # Get the value of the BytesIO buffer and write it to the response
        pdf = buffer.getvalue()
        buffer.close()

        response = HttpResponse(pdf, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="cases.pdf"'
        return response
    except Exception as e:
        logger.error(f"Error in export_to_pdf: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while exporting data to PDF.'})

@login_required
def item_usage_report(request):
    """
    Generates a report on item usage per dentist within a specified date range.
    Supports AJAX responses for dynamic updates.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        items = Item.objects.all()

        # Calculate total quantities across all items
        total_items_used = 0
        total_unique_items = 0
        top_items = []
        item_usage_details = []

        for item in items:
            case_items_query = CaseItem.objects.filter(item=item)

            if start_date:
                case_items_query = case_items_query.filter(case__received_date_time__gte=start_date)
            if end_date:
                case_items_query = case_items_query.filter(case__received_date_time__lte=end_date)

            total_quantity = case_items_query.aggregate(total=Sum('quantity'))['total'] or 0

            # Skip items with zero usage
            if total_quantity == 0:
                continue

            total_items_used += total_quantity
            total_unique_items += 1

            # Get usage by dentist
            dentist_usage = case_items_query.values(
                'case__dentist__id',
                'case__dentist__first_name',
                'case__dentist__last_name'
            ).annotate(
                quantity_sum=Sum('quantity')
            ).order_by('-quantity_sum')

            # Calculate usage by month
            monthly_usage = case_items_query.annotate(
                month=TruncMonth('case__received_date_time')
            ).values('month').annotate(
                monthly_quantity=Sum('quantity')
            ).order_by('month')

            # Convert to serializable format
            monthly_usage_serializable = []
            for month_data in monthly_usage:
                if month_data['month']:
                    month_str = month_data['month'].strftime('%Y-%m')
                    monthly_usage_serializable.append({
                        'month': month_str,
                        'quantity': month_data['monthly_quantity']
                    })

            # Convert dentist usage to serializable format
            dentist_usage_serializable = []
            for dentist in dentist_usage:
                dentist_full_name = f"{dentist['case__dentist__first_name']} {dentist['case__dentist__last_name']}"
                dentist_usage_serializable.append({
                    'dentist_id': dentist['case__dentist__id'],
                    'dentist_full_name': dentist_full_name,
                    'quantity_sum': dentist['quantity_sum']
                })

            # Create item details
            item_detail = {
                'item_name': item.name,
                'item_id': item.id,
                'total_quantity': total_quantity,
                'dentist_usage': dentist_usage_serializable,
                'monthly_usage': monthly_usage_serializable,
                'unit_cost': item.selling_price or 0,
                'total_cost': (item.selling_price or 0) * total_quantity
            }

            item_usage_details.append(item_detail)

            # Add to top items list
            top_items.append({
                'name': item.name,
                'quantity': total_quantity,
                'cost': item_detail['total_cost']
            })

        # Sort items by total quantity
        item_usage_details.sort(key=lambda x: x['total_quantity'], reverse=True)

        # Get top 5 items by quantity
        top_items.sort(key=lambda x: x['quantity'], reverse=True)
        top_items = top_items[:5]

        # Calculate total cost
        total_cost = sum(item['total_cost'] for item in item_usage_details)

        # Get unique dentists who used items
        all_dentist_ids = set()
        for item in item_usage_details:
            for dentist in item['dentist_usage']:
                all_dentist_ids.add(dentist['dentist_id'])
        total_dentists = len(all_dentist_ids)

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            # Return JSON response for AJAX request
            return JsonResponse({
                'item_usage_details': item_usage_details,
                'total_items_used': total_items_used,
                'total_unique_items': total_unique_items,
                'total_cost': total_cost,
                'total_dentists': total_dentists,
                'top_items': top_items
            }, safe=False)
        else:
            # Render HTML template for normal request
            context = {
                'item_usage_details': item_usage_details,
                'start_date': start_date,
                'end_date': end_date,
                'total_items_used': total_items_used,
                'total_unique_items': total_unique_items,
                'total_cost': total_cost,
                'total_dentists': total_dentists,
                'top_items': top_items,
                'days_in_period': (end_date - start_date).days + 1,
                'daily_average': round(total_items_used / ((end_date - start_date).days + 1), 1) if (end_date - start_date).days > 0 else total_items_used
            }
            return render(request, 'reports/item_usage_report.html', context)
    except Exception as e:
        logger.error(f"Error in item_usage_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the item usage report.'})

@login_required
def dentist_treemap_chart(request):
    """
    Generates data for a treemap chart representing dentists' revenue, converting currencies as needed.
    """
    try:
        base_currency_code = 'ALL'
        dentist_values = Case.objects.annotate(
            dentist_name=Concat('dentist__first_name', Value(' '), 'dentist__last_name'),
            currency_code=F('invoice__currency__code'),
            original_value=Coalesce('invoice__total_amount', Value(decimal.Decimal('0')), output_field=DecimalField())
        ).values(
            'dentist_name', 'currency_code'
        ).annotate(
            total_value=Sum('original_value', output_field=DecimalField())
        )

        dentist_values_converted = {}
        for case in dentist_values:
            dentist = case['dentist_name']
            currency_code = case['currency_code'] or base_currency_code
            total_value = case['total_value'] or decimal.Decimal('0')

            if currency_code != base_currency_code and base_currency_code != 'ALL':
                try:
                    rate = ExchangeRate.objects.get(
                        from_currency__code=currency_code,
                        to_currency__code=base_currency_code
                    ).rate
                except ExchangeRate.DoesNotExist:
                    rate = decimal.Decimal('1.0')
                    logger.warning(f"Exchange rate from {currency_code} to {base_currency_code} not found. Using 1.0.")
                converted_value = (total_value * rate).quantize(decimal.Decimal('.01'))
            else:
                converted_value = total_value.quantize(decimal.Decimal('.01'))

            if dentist in dentist_values_converted:
                dentist_values_converted[dentist] += converted_value
            else:
                dentist_values_converted[dentist] = converted_value

        dentist_values_json = json.dumps(dentist_values_converted, cls=DjangoJSONEncoder)

        logger.info(f"Dentist Treemap Chart data: {dentist_values_json}")

        context = {
            'dentist_values_json': dentist_values_json,
        }

        return render(request, 'reports/dentist_treemap_chart.html', context)
    except Exception as e:
        logger.error(f"Error in dentist_treemap_chart: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the dentist treemap chart.'})

from django.shortcuts import render
from django.contrib.auth.decorators import login_required
from django.db.models import Q, Sum, DecimalField
from django.db.models.functions import TruncMonth
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, date, timedelta
import logging



logger = logging.getLogger(__name__)

@login_required
def financial_report(request):
    """
    Generates a financial report with total case value, invoices, payments, and monthly breakdown.
    """
    try:
        months = range(1, 13)
        base_currency_code = 'ALL'
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')

        # Parse dates and make them timezone-aware
        start_date = timezone.make_aware(datetime.strptime(start_date_str, '%Y-%m-%d')) if start_date_str else None
        end_date = timezone.make_aware(datetime.strptime(end_date_str, '%Y-%m-%d')) if end_date_str else None

        date_filters = Q()
        if start_date and end_date:
            date_filters = Q(date__gte=start_date, date__lte=end_date)
        elif start_date:
            date_filters = Q(date__gte=start_date)
        elif end_date:
            date_filters = Q(date__lte=end_date)

        total_case_value = get_total_value_of_all_cases(base_currency_code, start_date, end_date)
        total_invoices_amount = get_total_invoices_amount(base_currency_code, date_filters)
        total_payments = get_total_payments(base_currency_code, start_date, end_date)

        # Calculate percentages relative to total case value
        total_invoices_percentage = (total_invoices_amount / total_case_value * 100) if total_case_value else Decimal('0.00')
        total_payments_percentage = (total_payments / total_case_value * 100) if total_case_value else Decimal('0.00')

        # Monthly breakdown
        monthly_data = Invoice.objects.filter(date_filters) \
            .annotate(month=TruncMonth('date')) \
            .values('month') \
            .annotate(total_value=Sum('total_amount', output_field=DecimalField())) \
            .order_by('month')

        # Calculate the total value for all months
        total_monthly_value = monthly_data.aggregate(total=Sum('total_value'))['total'] or Decimal('0.00')

        context = {
            'total_case_value': total_case_value,
            'total_invoices_amount': total_invoices_amount,
            'total_payments': total_payments,
            'total_invoices_percentage': total_invoices_percentage,
            'total_payments_percentage': total_payments_percentage,
            'base_currency_code': base_currency_code,
            'start_date': start_date,
            'end_date': end_date,
            'months': months,
            'monthly_data': monthly_data,
            'total_monthly_value': total_monthly_value,
        }

        return render(request, 'reports/financial_report.html', context)
    except Exception as e:
        logger.error(f"Error in financial_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the financial report.'})

# Utility functions

def get_total_value_of_all_cases(base_currency_code, start_date, end_date):
    invoices = Invoice.objects.all()
    if start_date:
        invoices = invoices.filter(date__gte=start_date)
    if end_date:
        invoices = invoices.filter(date__lte=end_date)
    return invoices.aggregate(total=Sum('total_amount', output_field=DecimalField()))['total'] or Decimal('0.00')

def get_total_invoices_amount(base_currency_code, date_filters):
    return Invoice.objects.filter(date_filters).aggregate(
        total=Sum('total_amount', output_field=DecimalField())
    )['total'] or Decimal('0.00')

def get_total_payments(base_currency_code, start_date, end_date):
    payments = Payment.objects.all()
    if start_date:
        payments = payments.filter(date__gte=start_date)
    if end_date:
        payments = payments.filter(date__lte=end_date)
    return payments.aggregate(total=Sum('amount', output_field=DecimalField()))['total'] or Decimal('0.00')
@login_required
def detailed_financial_report(request):
    """
    Generates a detailed financial report with total values and monthly breakdown.
    """
    try:
        base_currency_code = 'ALL'
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=365))
        end_date = parse_date(end_date_str) or date.today()

        date_filters = Q()
        if start_date and end_date:
            date_filters &= Q(date__range=[start_date, end_date])
        elif start_date:
            date_filters &= Q(date__gte=start_date)
        elif end_date:
            date_filters &= Q(date__lte=end_date)

        # Get all invoices in the date range
        invoices = Invoice.objects.filter(date_filters)

        # Calculate total values
        total_case_value = get_total_value_of_all_cases(base_currency_code, start_date, end_date)
        total_invoices_amount = get_total_invoices_amount(base_currency_code, date_filters)
        total_payments = get_total_payments(base_currency_code, start_date, end_date)

        # Calculate outstanding balance
        outstanding_balance = total_invoices_amount - total_payments
        payment_rate = (total_payments / total_invoices_amount * 100) if total_invoices_amount > 0 else 0

        # Get invoice counts by status
        invoice_status_counts = invoices.values('status').annotate(count=Count('id'))
        paid_invoices_count = sum(item['count'] for item in invoice_status_counts if item['status'] == 'PAID')
        partial_invoices_count = sum(item['count'] for item in invoice_status_counts if item['status'] == 'PARTIAL')
        unpaid_invoices_count = sum(item['count'] for item in invoice_status_counts if item['status'] == 'UNPAID')
        total_invoices_count = invoices.count()

        # Get payment data
        payments = Payment.objects.filter(date__range=[start_date, end_date])
        payment_methods = payments.values('payment_method').annotate(count=Count('id'), total=Sum('amount'))

        # Monthly breakdown
        monthly_data = invoices \
            .annotate(month=TruncMonth('date')) \
            .values('month') \
            .annotate(
                total_value=Sum('total_amount', output_field=DecimalField()),
                invoice_count=Count('id')
            ) \
            .order_by('month')

        # Monthly payments
        monthly_payments = Payment.objects.filter(date__range=[start_date, end_date]) \
            .annotate(month=TruncMonth('date')) \
            .values('month') \
            .annotate(
                total_paid=Sum('amount', output_field=DecimalField()),
                payment_count=Count('id')
            ) \
            .order_by('month')

        # Combine monthly data
        combined_monthly_data = []
        all_months = set(item['month'] for item in monthly_data) | set(item['month'] for item in monthly_payments)

        for month in sorted(all_months):
            invoice_data = next((item for item in monthly_data if item['month'] == month), {'total_value': decimal.Decimal('0.00'), 'invoice_count': 0})
            payment_data = next((item for item in monthly_payments if item['month'] == month), {'total_paid': decimal.Decimal('0.00'), 'payment_count': 0})

            combined_monthly_data.append({
                'month': month,
                'total_value': invoice_data['total_value'],
                'invoice_count': invoice_data['invoice_count'],
                'total_paid': payment_data['total_paid'],
                'payment_count': payment_data['payment_count'],
                'balance': invoice_data['total_value'] - payment_data['total_paid']
            })

        # Calculate the total value for all months
        total_monthly_value = sum(item['total_value'] for item in combined_monthly_data)
        total_monthly_paid = sum(item['total_paid'] for item in combined_monthly_data)

        # Get top dentists by revenue
        top_dentists_by_revenue = Invoice.objects.filter(date_filters).values(
            'dentist__id', 'dentist__first_name', 'dentist__last_name'
        ).annotate(
            total_revenue=Sum('total_amount'),
            invoice_count=Count('id')
        ).order_by('-total_revenue')[:5]

        # Format dentist data
        top_dentists = []
        for dentist in top_dentists_by_revenue:
            if dentist['dentist__id'] is not None:
                dentist_name = f"{dentist['dentist__first_name']} {dentist['dentist__last_name']}"
                top_dentists.append({
                    'name': dentist_name,
                    'revenue': dentist['total_revenue'],
                    'invoice_count': dentist['invoice_count'],
                    'percentage': (dentist['total_revenue'] / total_invoices_amount * 100) if total_invoices_amount > 0 else 0
                })

        context = {
            'total_case_value': total_case_value,
            'total_invoices_amount': total_invoices_amount,
            'total_payments': total_payments,
            'outstanding_balance': outstanding_balance,
            'payment_rate': payment_rate,
            'paid_invoices_count': paid_invoices_count,
            'partial_invoices_count': partial_invoices_count,
            'unpaid_invoices_count': unpaid_invoices_count,
            'total_invoices_count': total_invoices_count,
            'payment_methods': payment_methods,
            'monthly_data': combined_monthly_data,
            'total_monthly_value': total_monthly_value,
            'total_monthly_paid': total_monthly_paid,
            'base_currency_code': base_currency_code,
            'start_date': start_date,
            'end_date': end_date,
            'top_dentists': top_dentists,
            'date_range_days': (end_date - start_date).days
        }

        return render(request, 'reports/detailed_financial_report.html', context)
    except Exception as e:
        logger.error(f"Error in detailed_financial_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the detailed financial report.'})

@login_required
def case_progress_report(request):
    """
    Generates a report on case progress, including status counts, average durations, and overdue cases.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str)
        end_date = parse_date(end_date_str)

        date_filters = Q()
        if start_date and end_date:
            date_filters &= Q(received_date_time__gte=start_date, received_date_time__lte=end_date)
        elif start_date:
            date_filters &= Q(received_date_time__gte=start_date)
        elif end_date:
            date_filters &= Q(received_date_time__lte=end_date)

        cases = Case.objects.filter(date_filters)

        # Case status counts
        status_counts = cases.values('status').annotate(count=Count('case_number')).order_by('-count')

        # Average time spent in each stage
        avg_time_per_stage = cases.annotate(
            stage_duration=ExpressionWrapper(
                F('actual_completion') - F('received_date_time'),
                output_field=DurationField()
            )
        ).values('current_stage__name').annotate(
            avg_duration=Avg('stage_duration')
        ).order_by('current_stage__name')

        # Overdue cases with days overdue calculation
        today = date.today()
        overdue_cases = cases.filter(status='in_progress', ship_date_time__lt=today).annotate(
            overdue_days=ExpressionWrapper(
                timezone.now().date() - F('ship_date_time__date'),
                output_field=DurationField()
            )
        )

        # Convert overdue_days to integer days
        overdue_cases_list = []
        for case in overdue_cases:
            if isinstance(case.overdue_days, timedelta):
                days_overdue = case.overdue_days.days
            else:
                days_overdue = 0
            overdue_cases_list.append({
                'case_number': case.case_number,
                'received_date_time': case.received_date_time,
                'ship_date_time': case.ship_date_time,
                'days_overdue': days_overdue,
            })

        # In-progress and on-hold cases
        in_progress_cases = cases.filter(status='in_progress', ship_date_time__gte=today)
        on_hold_cases = cases.filter(status='on_hold')

        context = {
            'status_counts': status_counts,
            'avg_time_per_stage': avg_time_per_stage,
            'overdue_cases': overdue_cases_list,
            'in_progress_cases': in_progress_cases,
            'on_hold_cases': on_hold_cases,
            'start_date': start_date,
            'end_date': end_date,
        }

        return render(request, 'reports/case_progress_report.html', context)
    except Exception as e:
        logger.error(f"Error in case_progress_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the case progress report.'})

@login_required
def inventory_management_report(request):
    """
    Generates a report on inventory management, including levels, usage trends, restocking needs, and consumption analysis.
    """
    try:
        # Get date range from request
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        # Get all items
        items = Item.objects.all()

        # Calculate total inventory value
        total_inventory_value = sum(item.stock_quantity * (item.selling_price or 0) for item in items)

        # Count items by category (using type as a fallback)
        item_categories = {}
        for item in items:
            # Check if the item has a category attribute, otherwise use type or 'Uncategorized'
            if hasattr(item, 'category') and item.category:
                category = item.category
            elif hasattr(item, 'type') and item.type:
                category = item.type
            else:
                category = 'Uncategorized'

            if category in item_categories:
                item_categories[category] += 1
            else:
                item_categories[category] = 1

        # Inventory levels
        inventory_levels = items.annotate(
            total_quantity=F('stock_quantity')
        ).order_by('total_quantity')

        # Calculate total items and average stock level
        total_items = inventory_levels.count()
        total_stock = inventory_levels.aggregate(total=Sum('stock_quantity'))['total'] or 0
        avg_stock_level = total_stock / total_items if total_items > 0 else 0

        # Usage trends (within date range)
        usage_trends = items.annotate(
            usage_in_period=Sum(
                'caseitem__quantity',
                filter=Q(caseitem__case__received_date_time__range=[start_date, end_date])
            )
        ).order_by('-usage_in_period')

        # Calculate total usage in period
        total_usage = usage_trends.aggregate(
            total=Sum('usage_in_period')
        )['total'] or 0

        # Restocking needs
        restocking_needs = items.annotate(
            restock_needed=ExpressionWrapper(
                F('minimum_stock_level') - F('stock_quantity'), output_field=DecimalField()
            )
        ).filter(restock_needed__gt=0).order_by('-restock_needed')

        # Count items needing restock
        restock_count = restocking_needs.count()

        # Calculate restock urgency levels
        urgent_restock = restocking_needs.filter(stock_quantity=0).count()
        high_priority = restocking_needs.filter(stock_quantity__gt=0, stock_quantity__lte=F('minimum_stock_level') * 0.5).count()
        medium_priority = restocking_needs.filter(stock_quantity__gt=F('minimum_stock_level') * 0.5).count()

        # Highlight items that are running low or overstocked
        low_stock_items = items.filter(stock_quantity__lte=F('minimum_stock_level'))
        low_stock_count = low_stock_items.count()

        overstocked_items = items.filter(stock_quantity__gte=F('maximum_stock_level'))
        overstocked_count = overstocked_items.count()

        # Calculate excess inventory value
        excess_inventory_value = sum(
            (item.stock_quantity - item.maximum_stock_level) * (item.selling_price or 0)
            for item in overstocked_items
        )

        # Raw material consumption and cost analysis
        raw_material_consumption = []
        total_cost = 0

        for item in items:
            # Get usage in period
            usage = CaseItem.objects.filter(
                item=item,
                case__received_date_time__range=[start_date, end_date]
            ).aggregate(total=Sum('quantity'))['total'] or 0

            # Calculate cost
            item_cost = item.cost() * usage if usage else 0
            total_cost += item_cost

            if usage > 0:
                raw_material_consumption.append({
                    'name': item.name,
                    'usage': usage,
                    'unit_cost': item.cost(),
                    'total_cost': item_cost,
                })

        # Sort by total cost (highest first)
        raw_material_consumption.sort(key=lambda x: x['total_cost'], reverse=True)

        # Get top 10 most used items
        top_items = sorted([
            {'name': item.name, 'usage': getattr(item, 'usage_in_period', 0) or 0}
            for item in usage_trends if getattr(item, 'usage_in_period', 0)
        ], key=lambda x: x['usage'], reverse=True)[:10]

        context = {
            'inventory_levels': inventory_levels,
            'usage_trends': usage_trends,
            'restocking_needs': restocking_needs,
            'low_stock_items': low_stock_items,
            'overstocked_items': overstocked_items,
            'raw_material_consumption': raw_material_consumption,
            'start_date': start_date,
            'end_date': end_date,
            'total_inventory_value': total_inventory_value,
            'total_items': total_items,
            'avg_stock_level': avg_stock_level,
            'total_usage': total_usage,
            'restock_count': restock_count,
            'urgent_restock': urgent_restock,
            'high_priority': high_priority,
            'medium_priority': medium_priority,
            'low_stock_count': low_stock_count,
            'overstocked_count': overstocked_count,
            'excess_inventory_value': excess_inventory_value,
            'total_cost': total_cost,
            'item_categories': item_categories,
            'top_items': top_items
        }

        return render(request, 'reports/inventory_management_report.html', context)
    except FieldError as fe:
        logger.error(f"FieldError in inventory_management_report: {fe}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the inventory management report. Please contact the administrator.'})
    except Exception as e:
        logger.error(f"Error in inventory_management_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the inventory management report.'})

from django.core.exceptions import FieldError  # Ensure this import exists

# reports/views.py

from django.shortcuts import render
from django.core.paginator import Paginator
from django.contrib.auth.decorators import login_required
from django.db.models import (
    Count,
    Avg,
    Sum,
    Q,
    F,
    Case as DjangoCase,  # Aliased to avoid confusion
    When,
    Value,
    ExpressionWrapper,
    DecimalField,
)
from django.core.exceptions import FieldError
import logging

logger = logging.getLogger(__name__)

@login_required
def dentist_performance_report(request):
    """
    Generates a performance report for dentists, including case counts, completion rates, feedback, and revenue.
    Highlights top performers and those needing support.
    """
    try:
        # Get all dentists
        dentists = Dentist.objects.all()

        # Calculate performance metrics with the correct related name
        dentist_performance = dentists.annotate(
            total_cases=Count('dentist_cases'),
            completed_cases=Count('dentist_cases', filter=Q(dentist_cases__status='closed')),
            average_patient_feedback=Avg('dentist_cases__patient_feedback'),
            total_revenue=Sum('invoices__total_amount'),
            specialization=F('clinic_name')  # Assuming 'clinic_name' represents specialization
        ).order_by('-total_cases')

        # Calculate completion rates in Python to avoid Django ORM issues
        for dentist in dentist_performance:
            dentist.case_completion_rate = (
                (dentist.completed_cases / dentist.total_cases * 100)
                if dentist.total_cases > 0 else 0
            )

        # Convert to list and sort by completion rate
        dentist_performance_list = list(dentist_performance)
        dentist_performance_list.sort(key=lambda x: x.case_completion_rate, reverse=True)

        # Highlight top-performing dentists and those needing support
        top_performers = [d for d in dentist_performance_list if d.case_completion_rate >= 80][:10]
        dentists_needing_support = [d for d in dentist_performance_list if d.case_completion_rate < 50]

        # Implement Pagination for Dentists Needing Support
        paginator = Paginator(dentists_needing_support, 10)  # Show 10 dentists per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        context = {
            'dentist_performance': dentist_performance_list,
            'top_performers': top_performers,
            'dentists_needing_support': page_obj,  # Use page_obj in template
        }

        return render(request, 'reports/dentist_performance_report.html', context)
    except FieldError as fe:
        logger.error(f"FieldError in dentist_performance_report: {fe}")
        return render(request, 'reports/error.html', {'error': 'A database field error occurred while generating the dentist performance report.'})
    except Exception as e:
        logger.error(f"Error in dentist_performance_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the dentist performance report.'})


class DentistFinancialSummaryView(LoginRequiredMixin, FormView):
    """Detailed financial summary for a specific dentist."""
    template_name = 'reports/dentist_financial_summary.html'
    form_class = DentistSelectionForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _('Dentist Financial Summary Report')
        # Initialize report variables as None or empty
        context['selected_dentist'] = None
        context['selected_currency'] = None
        context['invoices_data'] = []
        context['totals'] = {
            'invoiced': decimal.Decimal('0.00'),
            'paid': decimal.Decimal('0.00'),
            'due': decimal.Decimal('0.00'),
        }
        # Add all dentists and currencies to the context for the dropdowns
        context['all_dentists'] = Dentist.objects.all().order_by('first_name', 'last_name')
        context['all_currencies'] = Currency.objects.all().order_by('code')
        return context

    def get(self, request, *args, **kwargs):
        # Check if dentist ID and currency ID are in the request
        dentist_id = request.GET.get('dentist')
        currency_id = request.GET.get('currency')

        if dentist_id:
            try:
                selected_dentist = Dentist.objects.get(pk=dentist_id)

                # Get the selected currency or use default
                selected_currency = None
                if currency_id:
                    try:
                        selected_currency = Currency.objects.get(pk=currency_id)
                    except Currency.DoesNotExist:
                        logger.warning(f"Currency with ID {currency_id} not found")

                if not selected_currency:
                    # Use default currency (EUR or first available)
                    selected_currency = Currency.objects.filter(code='EUR').first() or Currency.objects.first()

                logger.info(f"Generating financial summary for Dentist: {selected_dentist.get_full_name()} in currency: {selected_currency.code}")
                return self.process_dentist_data(selected_dentist, selected_currency)
            except Dentist.DoesNotExist:
                logger.warning(f"Dentist with ID {dentist_id} not found")

        # If no dentist selected or invalid ID, show the form
        return super().get(request, *args, **kwargs)

    def form_valid(self, form):
        """
        After the user selects a dentist and currency, process the data and redisplay the template
        with the report results.
        """
        selected_dentist = form.cleaned_data['dentist']
        selected_currency = form.cleaned_data['currency']
        logger.info(f"Generating financial summary for Dentist: {selected_dentist.get_full_name()} in currency: {selected_currency.code}")
        return self.process_dentist_data(selected_dentist, selected_currency)

    def process_dentist_data(self, selected_dentist, selected_currency):
        # Get all cases for the selected dentist
        cases = Case.objects.filter(
            dentist=selected_dentist
        ).select_related(
            'patient'
        ).prefetch_related(
            'case_items__item',
            'invoice__currency',
            'invoice__finance_invoice_payments__payment'
        ).order_by('-received_date_time')  # Sort by received date

        cases_data = []
        total_case_value = decimal.Decimal('0.00')  # Total value of all cases
        total_invoiced = decimal.Decimal('0.00')    # Total amount already invoiced
        total_matured = decimal.Decimal('0.00')     # Total matured amount
        total_unmatured = decimal.Decimal('0.00')   # Total unmatured amount
        total_paid = decimal.Decimal('0.00')        # Total amount already paid
        total_due = decimal.Decimal('0.00')         # Total amount due on invoices
        total_overpaid = decimal.Decimal('0.00')    # Total overpaid amount
        total_uninvoiced = decimal.Decimal('0.00')  # Total value of cases not yet invoiced
        total_potential_payment = decimal.Decimal('0.00')  # Total potential payment (invoiced + uninvoiced)

        # Process each case
        for case in cases:
            try:
                # Calculate case value from case items
                case_value = case.total_cost
                if case_value is None:
                    case_value = decimal.Decimal('0.00')

                # Initialize variables
                invoice_id = None
                invoice_date = None
                invoice_status = 'No Invoice'
                invoice_amount = decimal.Decimal('0.00')
                matured_amount = decimal.Decimal('0.00')
                unmatured_amount = decimal.Decimal('0.00')
                paid_amount = decimal.Decimal('0.00')
                amount_due = decimal.Decimal('0.00')
                overpaid_amount = decimal.Decimal('0.00')
                currency_code = selected_currency.code
                invoice_url = None

                # Check if case has an invoice
                has_invoice = hasattr(case, 'invoice') and case.invoice is not None

                if has_invoice:
                    invoice = case.invoice
                    invoice_id = invoice.id
                    invoice_date = invoice.date
                    invoice_status = invoice.get_status_display()
                    invoice_amount = invoice.total_amount
                    currency_code = invoice.currency.code
                    invoice_url = reverse('billing:invoice_detail', args=[invoice.pk])

                    # Calculate matured and unmatured amounts
                    # For simplicity, we'll consider an invoice matured if it's older than 30 days
                    today = timezone.now().date()
                    if invoice_date and (today - invoice_date).days > 30:
                        matured_amount = invoice_amount
                    else:
                        unmatured_amount = invoice_amount

                    # Get paid amount and due amount
                    paid_amount = invoice.get_paid_amount()
                    amount_due = invoice.get_amount_due()

                    # Calculate overpaid amount
                    if paid_amount > invoice_amount:
                        overpaid_amount = paid_amount - invoice_amount

                # Prepare case data
                cases_data.append({
                    'case_number': case.case_number,
                    'patient_name': case.patient.get_full_name() if case.patient else 'N/A',
                    'received_date': case.received_date_time,
                    'status': case.get_status_display(),
                    'case_value': case_value,
                    'has_invoice': has_invoice,
                    'invoice_id': invoice_id,
                    'invoice_date': invoice_date,
                    'invoice_status': invoice_status,
                    'invoice_amount': invoice_amount,
                    'matured_amount': matured_amount,
                    'unmatured_amount': unmatured_amount,
                    'paid_amount': paid_amount,
                    'amount_due': amount_due,
                    'overpaid_amount': overpaid_amount,
                    'currency_code': currency_code,
                    'original_currency_code': currency_code,  # Ruajmë monedhën origjinale
                    'original_case_value': case_value,        # Ruajmë vlerën origjinale
                    'original_invoice_amount': invoice_amount, # Ruajmë vlerën origjinale
                    'original_paid_amount': paid_amount,       # Ruajmë vlerën origjinale
                    'original_amount_due': amount_due,         # Ruajmë vlerën origjinale
                    'case_url': reverse('case:case_detail', args=[case.case_number]),
                    'invoice_url': invoice_url,
                    'converted': False  # Tregon nëse vlerat janë konvertuar
                })

                # Sum totals
                total_case_value += case_value
                total_invoiced += invoice_amount
                total_matured += matured_amount
                total_unmatured += unmatured_amount
                total_paid += paid_amount
                total_due += amount_due
                total_overpaid += overpaid_amount

                # Calculate uninvoiced amount
                if not has_invoice and case_value > 0:
                    total_uninvoiced += case_value

            except Exception as e:
                logger.error(f"Error processing case {case.case_number} for dentist summary: {e}")
                # You could add an error row to the report or just skip it

        # Calculate total potential payment (invoiced + uninvoiced - already paid)
        total_potential_payment = total_invoiced + total_uninvoiced - total_paid

        # Convert all totals to the selected currency
        for i, case_data in enumerate(cases_data):
            if case_data['currency_code'] != selected_currency.code:
                # Shënoj që vlerat janë konvertuar
                cases_data[i]['converted'] = True

                # Convert case value
                cases_data[i]['case_value'] = convert_to_base_currency(
                    case_data['case_value'],
                    case_data['currency_code'],
                    selected_currency.code
                )
                # Convert invoice amount
                cases_data[i]['invoice_amount'] = convert_to_base_currency(
                    case_data['invoice_amount'],
                    case_data['currency_code'],
                    selected_currency.code
                )
                # Convert matured amount
                cases_data[i]['matured_amount'] = convert_to_base_currency(
                    case_data['matured_amount'],
                    case_data['currency_code'],
                    selected_currency.code
                )
                # Convert unmatured amount
                cases_data[i]['unmatured_amount'] = convert_to_base_currency(
                    case_data['unmatured_amount'],
                    case_data['currency_code'],
                    selected_currency.code
                )
                # Convert paid amount
                cases_data[i]['paid_amount'] = convert_to_base_currency(
                    case_data['paid_amount'],
                    case_data['currency_code'],
                    selected_currency.code
                )
                # Convert amount due
                cases_data[i]['amount_due'] = convert_to_base_currency(
                    case_data['amount_due'],
                    case_data['currency_code'],
                    selected_currency.code
                )
                # Convert overpaid amount
                cases_data[i]['overpaid_amount'] = convert_to_base_currency(
                    case_data['overpaid_amount'],
                    case_data['currency_code'],
                    selected_currency.code
                )
                # Update currency code (por ruajmë monedhën origjinale në original_currency_code)
                cases_data[i]['currency_code'] = selected_currency.code

        context = self.get_context_data()
        context['selected_dentist'] = selected_dentist
        context['selected_currency'] = selected_currency
        context['cases_data'] = cases_data
        context['totals'] = {
            'case_value': total_case_value,
            'invoiced': total_invoiced,
            'matured': total_matured,
            'unmatured': total_unmatured,
            'paid': total_paid,
            'due': total_due,
            'overpaid': total_overpaid,
            'uninvoiced': total_uninvoiced,
            'potential_payment': total_potential_payment,
            'currency_code': selected_currency.code
        }

        # Redisplay the same template with the report data
        return self.render_to_response(context)

    def get_initial(self):
        """ Pre-fill the form if dentist_id comes as a GET parameter """
        initial = super().get_initial()
        dentist_id = self.request.GET.get('dentist_id')
        if dentist_id:
            try:
                initial['dentist'] = Dentist.objects.get(pk=dentist_id)
            except Dentist.DoesNotExist:
                pass
        return initial


# ===== NEW ENHANCED REPORTS =====

@login_required
def profitability_analysis_report(request):
    """
    Advanced profitability analysis with profit margins by case type, cost analysis, and ROI metrics.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=90))
        end_date = parse_date(end_date_str) or date.today()

        # Get cases in date range
        cases = Case.objects.filter(received_date_time__date__range=[start_date, end_date])

        # Profitability by case type/item
        profitability_data = []
        total_revenue = decimal.Decimal('0.00')
        total_cost = decimal.Decimal('0.00')

        # Group by item types
        items = Item.objects.all()
        for item in items:
            case_items = CaseItem.objects.filter(
                item=item,
                case__received_date_time__date__range=[start_date, end_date]
            )

            if not case_items.exists():
                continue

            quantity_sold = case_items.aggregate(total=Sum('quantity'))['total'] or 0
            revenue = quantity_sold * item.selling_price
            cost = quantity_sold * (item.cost() if hasattr(item, 'cost') else item.selling_price * decimal.Decimal('0.6'))
            profit = revenue - cost
            margin = (profit / revenue * 100) if revenue > 0 else 0

            total_revenue += revenue
            total_cost += cost

            profitability_data.append({
                'item_name': item.name,
                'quantity_sold': quantity_sold,
                'revenue': revenue,
                'cost': cost,
                'profit': profit,
                'margin_percentage': margin,
                'roi': (profit / cost * 100) if cost > 0 else 0
            })

        # Sort by profit margin
        profitability_data.sort(key=lambda x: x['margin_percentage'], reverse=True)

        # Overall metrics
        total_profit = total_revenue - total_cost
        overall_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0

        # Break-even analysis
        fixed_costs = decimal.Decimal('10000.00')  # Estimate - should be configurable
        break_even_revenue = fixed_costs / (overall_margin / 100) if overall_margin > 0 else 0

        # Top and bottom performers
        top_performers = profitability_data[:5]
        bottom_performers = profitability_data[-5:] if len(profitability_data) > 5 else []

        context = {
            'profitability_data': profitability_data,
            'start_date': start_date,
            'end_date': end_date,
            'total_revenue': total_revenue,
            'total_cost': total_cost,
            'total_profit': total_profit,
            'overall_margin': overall_margin,
            'break_even_revenue': break_even_revenue,
            'top_performers': top_performers,
            'bottom_performers': bottom_performers,
        }

        return render(request, 'reports/profitability_analysis_report.html', context)
    except Exception as e:
        logger.error(f"Error in profitability_analysis_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the profitability analysis report.'})

@login_required
def cash_flow_forecast_report(request):
    """
    Cash flow forecast with predictive modeling and payment trend analysis.
    """
    try:
        # Get historical data for the last 12 months
        end_date = date.today()
        start_date = end_date - timedelta(days=365)

        # Historical monthly cash flow
        monthly_cash_flow = []
        current_month = start_date.replace(day=1)

        while current_month <= end_date:
            next_month = (current_month + timedelta(days=32)).replace(day=1)

            # Revenue for the month
            monthly_revenue = Invoice.objects.filter(
                date__gte=current_month,
                date__lt=next_month
            ).aggregate(total=Sum('total_amount'))['total'] or decimal.Decimal('0.00')

            # Payments for the month
            monthly_payments = Payment.objects.filter(
                date__gte=current_month,
                date__lt=next_month
            ).aggregate(total=Sum('amount'))['total'] or decimal.Decimal('0.00')

            # Net cash flow
            net_cash_flow = monthly_payments - (monthly_revenue * decimal.Decimal('0.3'))  # Assuming 30% costs

            monthly_cash_flow.append({
                'month': current_month,
                'revenue': monthly_revenue,
                'payments': monthly_payments,
                'net_cash_flow': net_cash_flow,
                'cumulative_cash_flow': sum(item['net_cash_flow'] for item in monthly_cash_flow) + net_cash_flow
            })

            current_month = next_month

        # Forecast next 6 months based on trends
        if len(monthly_cash_flow) >= 3:
            recent_avg_revenue = sum(item['revenue'] for item in monthly_cash_flow[-3:]) / 3
            recent_avg_payments = sum(item['payments'] for item in monthly_cash_flow[-3:]) / 3

            forecast_months = []
            for i in range(1, 7):  # Next 6 months
                forecast_month = (end_date + timedelta(days=30*i)).replace(day=1)

                # Simple trend-based forecast (can be enhanced with ML)
                growth_rate = decimal.Decimal('1.02')  # 2% monthly growth assumption
                forecast_revenue = recent_avg_revenue * (growth_rate ** i)
                forecast_payments = recent_avg_payments * (growth_rate ** i)
                forecast_net = forecast_payments - (forecast_revenue * decimal.Decimal('0.3'))

                forecast_months.append({
                    'month': forecast_month,
                    'revenue': forecast_revenue,
                    'payments': forecast_payments,
                    'net_cash_flow': forecast_net,
                    'is_forecast': True
                })
        else:
            forecast_months = []

        # Payment trend analysis
        payment_trends = Payment.objects.filter(
            date__gte=start_date
        ).values('payment_method').annotate(
            total_amount=Sum('amount'),
            count=Count('id'),
            avg_amount=Avg('amount')
        ).order_by('-total_amount')

        # Seasonal patterns
        seasonal_data = []
        for month in range(1, 13):
            month_data = [item for item in monthly_cash_flow if item['month'].month == month]
            if month_data:
                avg_revenue = sum(item['revenue'] for item in month_data) / len(month_data)
                seasonal_data.append({
                    'month': month,
                    'avg_revenue': avg_revenue
                })

        context = {
            'monthly_cash_flow': monthly_cash_flow,
            'forecast_months': forecast_months,
            'payment_trends': payment_trends,
            'seasonal_data': seasonal_data,
            'start_date': start_date,
            'end_date': end_date,
        }

        return render(request, 'reports/cash_flow_forecast_report.html', context)
    except Exception as e:
        logger.error(f"Error in cash_flow_forecast_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the cash flow forecast report.'})

@login_required
def quality_metrics_dashboard(request):
    """
    Quality control dashboard with revision rates, satisfaction scores, and quality trends.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=90))
        end_date = parse_date(end_date_str) or date.today()

        cases = Case.objects.filter(received_date_time__date__range=[start_date, end_date])

        # Overall quality metrics
        total_cases = cases.count()
        total_revisions = cases.aggregate(total=Sum('revision_count'))['total'] or 0
        revision_rate = (total_revisions / total_cases * 100) if total_cases > 0 else 0

        avg_patient_feedback = cases.aggregate(avg=Avg('patient_feedback'))['avg'] or 0
        avg_dentist_feedback = cases.aggregate(avg=Avg('dentist_feedback'))['avg'] or 0

        # Quality by dentist
        dentist_quality = cases.values(
            'dentist__first_name', 'dentist__last_name'
        ).annotate(
            case_count=Count('case_number'),
            total_revisions=Sum('revision_count'),
            avg_patient_feedback=Avg('patient_feedback'),
            avg_dentist_feedback=Avg('dentist_feedback')
        ).order_by('case_count')

        # Format dentist names and calculate revision rates
        for dentist in dentist_quality:
            dentist['full_name'] = f"{dentist['dentist__first_name']} {dentist['dentist__last_name']}"
            # Calculate revision rate
            dentist['revision_rate'] = (
                (dentist['total_revisions'] or 0) / dentist['case_count'] * 100
            ) if dentist['case_count'] > 0 else 0

        # Quality trends over time
        monthly_quality = cases.annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            case_count=Count('case_number'),
            total_revisions=Sum('revision_count'),
            avg_patient_feedback=Avg('patient_feedback'),
            avg_dentist_feedback=Avg('dentist_feedback')
        ).order_by('month')

        # Calculate monthly revision rates
        for month_data in monthly_quality:
            month_data['revision_rate'] = (
                month_data['total_revisions'] / month_data['case_count'] * 100
            ) if month_data['case_count'] > 0 else 0

        # Quality by case type/item
        item_quality = CaseItem.objects.filter(
            case__received_date_time__date__range=[start_date, end_date]
        ).values('item__name').annotate(
            case_count=Count('case', distinct=True),
            total_revisions=Sum('case__revision_count'),
            avg_patient_feedback=Avg('case__patient_feedback'),
            avg_dentist_feedback=Avg('case__dentist_feedback')
        ).order_by('-case_count')

        # Calculate revision rates for items
        for item_data in item_quality:
            item_data['revision_rate'] = (
                item_data['total_revisions'] / item_data['case_count'] * 100
            ) if item_data['case_count'] > 0 else 0

        # Quality alerts (high revision rates)
        quality_alerts = []
        for dentist in dentist_quality:
            if dentist['revision_rate'] > 15:  # Alert threshold
                quality_alerts.append({
                    'type': 'High Revision Rate',
                    'dentist': dentist['full_name'],
                    'value': dentist['revision_rate'],
                    'severity': 'high' if dentist['revision_rate'] > 25 else 'medium'
                })

        # Low satisfaction alerts
        for dentist in dentist_quality:
            if dentist['avg_patient_feedback'] and dentist['avg_patient_feedback'] < 3.0:
                quality_alerts.append({
                    'type': 'Low Patient Satisfaction',
                    'dentist': dentist['full_name'],
                    'value': dentist['avg_patient_feedback'],
                    'severity': 'high' if dentist['avg_patient_feedback'] < 2.5 else 'medium'
                })

        context = {
            'start_date': start_date,
            'end_date': end_date,
            'total_cases': total_cases,
            'total_revisions': total_revisions,
            'revision_rate': revision_rate,
            'avg_patient_feedback': avg_patient_feedback,
            'avg_dentist_feedback': avg_dentist_feedback,
            'dentist_quality': dentist_quality,
            'monthly_quality': list(monthly_quality),
            'item_quality': item_quality,
            'quality_alerts': quality_alerts,
        }

        return render(request, 'reports/quality_metrics_dashboard.html', context)
    except Exception as e:
        logger.error(f"Error in quality_metrics_dashboard: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the quality metrics dashboard.'})

@login_required
def workflow_efficiency_analysis(request):
    """
    Workflow efficiency analysis with bottleneck identification and stage-wise performance.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=30))
        end_date = parse_date(end_date_str) or date.today()

        # Get cases with workflow data
        cases = Case.objects.filter(
            received_date_time__date__range=[start_date, end_date]
        ).select_related('current_stage')

        # Stage performance analysis
        stage_performance = cases.values('current_stage__name').annotate(
            case_count=Count('case_number'),
            avg_time_in_stage=Avg(
                ExpressionWrapper(
                    timezone.now() - F('received_date_time'),
                    output_field=DurationField()
                )
            )
        ).order_by('-case_count')

        # Convert durations to hours for display
        for stage in stage_performance:
            if stage['avg_time_in_stage']:
                stage['avg_hours'] = stage['avg_time_in_stage'].total_seconds() / 3600
            else:
                stage['avg_hours'] = 0

        # Bottleneck identification (stages with longest average times)
        bottlenecks = sorted(stage_performance, key=lambda x: x['avg_hours'], reverse=True)[:3]

        # Workflow efficiency by department
        from case.models import Department
        department_efficiency = []

        for dept in Department.objects.all():
            dept_cases = cases.filter(current_stage__department=dept)
            if dept_cases.exists():
                completed_cases = dept_cases.filter(status='completed')
                completion_rate = (completed_cases.count() / dept_cases.count() * 100) if dept_cases.count() > 0 else 0

                avg_cycle_time = completed_cases.annotate(
                    cycle_time=ExpressionWrapper(
                        F('actual_completion') - F('received_date_time'),
                        output_field=DurationField()
                    )
                ).aggregate(avg=Avg('cycle_time'))['avg']

                department_efficiency.append({
                    'department': dept.name,
                    'total_cases': dept_cases.count(),
                    'completed_cases': completed_cases.count(),
                    'completion_rate': completion_rate,
                    'avg_cycle_hours': avg_cycle_time.total_seconds() / 3600 if avg_cycle_time else 0,
                    'efficiency_score': completion_rate * 0.7 + (100 - min(avg_cycle_time.total_seconds() / 3600, 100)) * 0.3 if avg_cycle_time else completion_rate * 0.7
                })

        # Sort by efficiency score
        department_efficiency.sort(key=lambda x: x['efficiency_score'], reverse=True)

        # Resource utilization (simplified)
        total_capacity_hours = 8 * 5 * len(Department.objects.all())  # 8 hours/day, 5 days/week per department
        actual_work_hours = sum(stage['case_count'] * stage['avg_hours'] for stage in stage_performance)
        utilization_rate = (actual_work_hours / total_capacity_hours * 100) if total_capacity_hours > 0 else 0

        # Efficiency trends over time
        weekly_efficiency = []
        current_week = start_date
        while current_week <= end_date:
            week_end = min(current_week + timedelta(days=6), end_date)

            week_cases = cases.filter(received_date_time__date__range=[current_week, week_end])
            week_completed = week_cases.filter(status='completed')

            weekly_efficiency.append({
                'week_start': current_week,
                'total_cases': week_cases.count(),
                'completed_cases': week_completed.count(),
                'completion_rate': (week_completed.count() / week_cases.count() * 100) if week_cases.count() > 0 else 0
            })

            current_week = week_end + timedelta(days=1)

        context = {
            'start_date': start_date,
            'end_date': end_date,
            'stage_performance': stage_performance,
            'bottlenecks': bottlenecks,
            'department_efficiency': department_efficiency,
            'utilization_rate': utilization_rate,
            'weekly_efficiency': weekly_efficiency,
            'total_cases': cases.count(),
            'completed_cases': cases.filter(status='completed').count(),
        }

        return render(request, 'reports/workflow_efficiency_analysis.html', context)
    except Exception as e:
        logger.error(f"Error in workflow_efficiency_analysis: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the workflow efficiency analysis.'})

@login_required
def inventory_optimization_report(request):
    """
    Advanced inventory optimization with ABC analysis, seasonal patterns, and cost optimization.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=365))
        end_date = parse_date(end_date_str) or date.today()

        # Get all items with usage data
        items = Item.objects.all()
        inventory_analysis = []

        for item in items:
            # Calculate usage in period
            usage_data = CaseItem.objects.filter(
                item=item,
                case__received_date_time__date__range=[start_date, end_date]
            ).aggregate(
                total_usage=Sum('quantity'),
                usage_count=Count('id')
            )

            total_usage = usage_data['total_usage'] or 0
            usage_frequency = usage_data['usage_count'] or 0

            # Calculate costs
            holding_cost = decimal.Decimal(str(item.stock_quantity)) * (item.selling_price or decimal.Decimal('0')) * decimal.Decimal('0.02')  # 2% holding cost
            ordering_cost = decimal.Decimal('50.00')  # Fixed ordering cost
            annual_usage = total_usage * (365 / max((end_date - start_date).days, 1))

            # EOQ calculation
            if annual_usage > 0 and item.selling_price:
                eoq = float(((2 * annual_usage * float(ordering_cost)) / float(holding_cost)) ** 0.5) if holding_cost > 0 else 0
            else:
                eoq = 0

            # ABC Classification based on usage value
            usage_value = decimal.Decimal(str(total_usage)) * (item.selling_price or decimal.Decimal('0'))

            # Seasonal analysis (simplified)
            monthly_usage = CaseItem.objects.filter(
                item=item,
                case__received_date_time__date__range=[start_date, end_date]
            ).annotate(
                month=TruncMonth('case__received_date_time')
            ).values('month').annotate(
                monthly_quantity=Sum('quantity')
            ).order_by('month')

            # Calculate seasonality index
            if monthly_usage:
                avg_monthly = sum(m['monthly_quantity'] for m in monthly_usage) / len(monthly_usage)
                seasonality_variance = sum((m['monthly_quantity'] - avg_monthly) ** 2 for m in monthly_usage) / len(monthly_usage)
                seasonality_index = (seasonality_variance ** 0.5) / avg_monthly if avg_monthly > 0 else 0
            else:
                seasonality_index = 0

            inventory_analysis.append({
                'item_name': item.name,
                'current_stock': item.stock_quantity,
                'min_stock': item.minimum_stock_level,
                'max_stock': item.maximum_stock_level,
                'total_usage': total_usage,
                'usage_frequency': usage_frequency,
                'usage_value': usage_value,
                'annual_usage_forecast': annual_usage,
                'eoq': eoq,
                'holding_cost': holding_cost,
                'seasonality_index': seasonality_index,
                'reorder_point': annual_usage / 12 + item.minimum_stock_level,  # Simplified reorder point
                'excess_stock': max(0, item.stock_quantity - item.maximum_stock_level),
                'stockout_risk': 'High' if item.stock_quantity <= item.minimum_stock_level else 'Low',
                'monthly_usage': list(monthly_usage)
            })

        # ABC Classification
        inventory_analysis.sort(key=lambda x: x['usage_value'], reverse=True)
        total_value = sum(item['usage_value'] for item in inventory_analysis)

        cumulative_value = 0
        for i, item in enumerate(inventory_analysis):
            cumulative_value += item['usage_value']
            cumulative_percentage = (cumulative_value / total_value * 100) if total_value > 0 else 0

            if cumulative_percentage <= 80:
                item['abc_class'] = 'A'
            elif cumulative_percentage <= 95:
                item['abc_class'] = 'B'
            else:
                item['abc_class'] = 'C'

        # Summary statistics
        class_a_items = [item for item in inventory_analysis if item['abc_class'] == 'A']
        class_b_items = [item for item in inventory_analysis if item['abc_class'] == 'B']
        class_c_items = [item for item in inventory_analysis if item['abc_class'] == 'C']

        # Cost optimization opportunities
        optimization_opportunities = []

        for item in inventory_analysis:
            if item['excess_stock'] > 0:
                optimization_opportunities.append({
                    'type': 'Excess Stock',
                    'item': item['item_name'],
                    'current_stock': item['current_stock'],
                    'recommended_stock': item['max_stock'],
                    'potential_savings': decimal.Decimal(str(item['excess_stock'])) * (decimal.Decimal(str(item['usage_value'])) / decimal.Decimal(str(max(item['total_usage'], 1)))) * decimal.Decimal('0.02')
                })

            if item['stockout_risk'] == 'High':
                optimization_opportunities.append({
                    'type': 'Stockout Risk',
                    'item': item['item_name'],
                    'current_stock': item['current_stock'],
                    'recommended_stock': item['reorder_point'],
                    'urgency': 'High'
                })

        context = {
            'inventory_analysis': inventory_analysis,
            'class_a_items': class_a_items,
            'class_b_items': class_b_items,
            'class_c_items': class_c_items,
            'optimization_opportunities': optimization_opportunities,
            'start_date': start_date,
            'end_date': end_date,
            'total_inventory_value': sum(decimal.Decimal(str(item['current_stock'])) * (decimal.Decimal(str(item['usage_value'])) / decimal.Decimal(str(max(item['total_usage'], 1)))) for item in inventory_analysis),
            'total_holding_cost': sum(item['holding_cost'] for item in inventory_analysis),
        }

        return render(request, 'reports/inventory_optimization_report.html', context)
    except Exception as e:
        logger.error(f"Error in inventory_optimization_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the inventory optimization report.'})

@login_required
def demand_forecasting_report(request):
    """
    Demand forecasting with trend analysis and capacity planning recommendations.
    """
    try:
        # Get historical data for the last 24 months
        end_date = date.today()
        start_date = end_date - timedelta(days=730)  # 2 years

        # Monthly case volume historical data
        monthly_cases = Case.objects.filter(
            received_date_time__date__gte=start_date
        ).annotate(
            month=TruncMonth('received_date_time')
        ).values('month').annotate(
            case_count=Count('case_number'),
            revenue=Sum('invoice__total_amount')
        ).order_by('month')

        # Convert to list for easier manipulation
        historical_data = list(monthly_cases)

        # Simple trend analysis and forecasting
        if len(historical_data) >= 6:
            # Calculate trend using linear regression (simplified)
            x_values = list(range(len(historical_data)))
            y_values = [item['case_count'] for item in historical_data]

            # Simple linear trend calculation
            n = len(x_values)
            sum_x = sum(x_values)
            sum_y = sum(y_values)
            sum_xy = sum(x * y for x, y in zip(x_values, y_values))
            sum_x2 = sum(x * x for x in x_values)

            # Linear regression coefficients
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x) if (n * sum_x2 - sum_x * sum_x) != 0 else 0
            intercept = (sum_y - slope * sum_x) / n

            # Forecast next 6 months
            forecast_data = []
            for i in range(1, 7):
                forecast_month = (end_date + timedelta(days=30*i)).replace(day=1)
                forecast_cases = max(0, intercept + slope * (len(historical_data) + i - 1))

                # Add seasonal adjustment (simplified)
                month_num = forecast_month.month
                seasonal_data = [item for item in historical_data if item['month'].month == month_num]
                if seasonal_data:
                    seasonal_avg = sum(item['case_count'] for item in seasonal_data) / len(seasonal_data)
                    overall_avg = sum(item['case_count'] for item in historical_data) / len(historical_data)
                    seasonal_factor = seasonal_avg / overall_avg if overall_avg > 0 else 1
                    forecast_cases *= seasonal_factor

                forecast_data.append({
                    'month': forecast_month,
                    'forecast_cases': int(forecast_cases),
                    'confidence': max(0.5, 1 - (i * 0.1))  # Decreasing confidence over time
                })
        else:
            forecast_data = []
            slope = 0

        # Seasonal analysis
        seasonal_patterns = {}
        for month in range(1, 13):
            month_data = [item for item in historical_data if item['month'].month == month]
            if month_data:
                avg_cases = sum(item['case_count'] for item in month_data) / len(month_data)
                seasonal_patterns[month] = {
                    'month': month,
                    'avg_cases': avg_cases,
                    'data_points': len(month_data)
                }

        # Capacity planning recommendations
        if forecast_data:
            max_forecast = max(item['forecast_cases'] for item in forecast_data)
            current_capacity = 100  # Assume current capacity (should be configurable)

            capacity_recommendations = []
            if max_forecast > current_capacity * 0.8:  # 80% capacity threshold
                capacity_recommendations.append({
                    'type': 'Capacity Increase',
                    'current_capacity': current_capacity,
                    'recommended_capacity': int(max_forecast * 1.2),  # 20% buffer
                    'reason': 'Forecasted demand exceeds 80% of current capacity'
                })

            # Staffing recommendations
            cases_per_staff_per_month = 20  # Assumption
            required_staff = max_forecast / cases_per_staff_per_month
            current_staff = 5  # Assumption (should be from actual data)

            if required_staff > current_staff:
                capacity_recommendations.append({
                    'type': 'Staffing Increase',
                    'current_staff': current_staff,
                    'recommended_staff': int(required_staff),
                    'reason': 'Forecasted demand requires additional staff'
                })
        else:
            capacity_recommendations = []

        # Growth analysis
        if len(historical_data) >= 12:
            recent_avg = sum(item['case_count'] for item in historical_data[-6:]) / 6
            older_avg = sum(item['case_count'] for item in historical_data[-12:-6]) / 6
            growth_rate = ((recent_avg - older_avg) / older_avg * 100) if older_avg > 0 else 0
        else:
            growth_rate = 0

        context = {
            'historical_data': historical_data,
            'forecast_data': forecast_data,
            'seasonal_patterns': seasonal_patterns,
            'capacity_recommendations': capacity_recommendations,
            'growth_rate': growth_rate,
            'trend_slope': slope,
            'start_date': start_date,
            'end_date': end_date,
        }

        return render(request, 'reports/demand_forecasting_report.html', context)
    except Exception as e:
        logger.error(f"Error in demand_forecasting_report: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the demand forecasting report.'})

@login_required
def customer_relationship_analysis(request):
    """
    Customer relationship analysis with lifetime value, retention, and growth opportunities.
    """
    try:
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        start_date = parse_date(start_date_str) or (date.today() - timedelta(days=365))
        end_date = parse_date(end_date_str) or date.today()

        # Get all dentists (customers)
        dentists = Dentist.objects.all()
        customer_analysis = []

        for dentist in dentists:
            # Get cases and invoices for this dentist
            cases = Case.objects.filter(
                dentist=dentist,
                received_date_time__date__range=[start_date, end_date]
            )

            invoices = Invoice.objects.filter(
                dentist=dentist,
                date__range=[start_date, end_date]
            )

            if not cases.exists() and not invoices.exists():
                continue

            # Calculate metrics
            total_cases = cases.count()
            total_revenue = invoices.aggregate(total=Sum('total_amount'))['total'] or decimal.Decimal('0.00')
            total_paid = invoices.filter(status='PAID').aggregate(total=Sum('total_amount'))['total'] or decimal.Decimal('0.00')

            # Customer lifetime value (simplified)
            first_case = cases.order_by('received_date_time').first()
            if first_case:
                customer_age_days = (end_date - first_case.received_date_time.date()).days
                customer_age_months = max(1, customer_age_days / 30)
                monthly_revenue = total_revenue / decimal.Decimal(str(customer_age_months))
                clv = monthly_revenue * 24  # Assume 24-month lifetime
            else:
                customer_age_days = 0
                monthly_revenue = decimal.Decimal('0.00')
                clv = decimal.Decimal('0.00')

            # Payment behavior
            overdue_invoices = invoices.filter(
                status='UNPAID',
                date__lt=timezone.now().date() - timedelta(days=30)
            ).count()

            payment_score = 100
            if invoices.count() > 0:
                paid_ratio = invoices.filter(status='PAID').count() / invoices.count()
                payment_score = paid_ratio * 100
                if overdue_invoices > 0:
                    payment_score -= (overdue_invoices * 10)  # Penalty for overdue
                payment_score = max(0, payment_score)

            # Frequency analysis
            if total_cases > 0 and customer_age_days > 0:
                case_frequency = total_cases / (customer_age_days / 30)  # Cases per month
            else:
                case_frequency = 0

            # Recent activity
            recent_cases = cases.filter(
                received_date_time__date__gte=end_date - timedelta(days=90)
            ).count()

            # Risk assessment
            risk_level = 'Low'
            if recent_cases == 0 and customer_age_days > 90:
                risk_level = 'High'  # No recent activity
            elif payment_score < 70:
                risk_level = 'Medium'  # Payment issues
            elif case_frequency < 0.5:  # Less than 0.5 cases per month
                risk_level = 'Medium'

            # Growth opportunity
            growth_potential = 'Low'
            if case_frequency > 2 and payment_score > 80:
                growth_potential = 'High'
            elif case_frequency > 1 and payment_score > 70:
                growth_potential = 'Medium'

            customer_analysis.append({
                'dentist_id': dentist.id,
                'dentist_name': f"{dentist.first_name} {dentist.last_name}",
                'clinic_name': dentist.clinic_name or 'N/A',
                'total_cases': total_cases,
                'total_revenue': total_revenue,
                'total_paid': total_paid,
                'clv': clv,
                'monthly_revenue': monthly_revenue,
                'customer_age_days': customer_age_days,
                'case_frequency': case_frequency,
                'payment_score': payment_score,
                'recent_cases': recent_cases,
                'overdue_invoices': overdue_invoices,
                'risk_level': risk_level,
                'growth_potential': growth_potential,
                'last_case_date': cases.order_by('-received_date_time').first().received_date_time.date() if cases.exists() else None
            })

        # Sort by CLV
        customer_analysis.sort(key=lambda x: x['clv'], reverse=True)

        # Segment customers
        high_value_customers = [c for c in customer_analysis if c['clv'] > 5000]
        medium_value_customers = [c for c in customer_analysis if 1000 <= c['clv'] <= 5000]
        low_value_customers = [c for c in customer_analysis if c['clv'] < 1000]

        # Risk analysis
        high_risk_customers = [c for c in customer_analysis if c['risk_level'] == 'High']
        medium_risk_customers = [c for c in customer_analysis if c['risk_level'] == 'Medium']

        # Growth opportunities
        high_growth_customers = [c for c in customer_analysis if c['growth_potential'] == 'High']

        # Retention analysis
        active_customers = len([c for c in customer_analysis if c['recent_cases'] > 0])
        total_customers = len(customer_analysis)
        retention_rate = (active_customers / total_customers * 100) if total_customers > 0 else 0

        # Revenue concentration
        total_all_revenue = sum(c['total_revenue'] for c in customer_analysis)
        top_10_revenue = sum(c['total_revenue'] for c in customer_analysis[:10])
        revenue_concentration = (top_10_revenue / total_all_revenue * 100) if total_all_revenue > 0 else 0

        context = {
            'customer_analysis': customer_analysis,
            'high_value_customers': high_value_customers,
            'medium_value_customers': medium_value_customers,
            'low_value_customers': low_value_customers,
            'high_risk_customers': high_risk_customers,
            'medium_risk_customers': medium_risk_customers,
            'high_growth_customers': high_growth_customers,
            'retention_rate': retention_rate,
            'revenue_concentration': revenue_concentration,
            'total_customers': total_customers,
            'active_customers': active_customers,
            'start_date': start_date,
            'end_date': end_date,
        }

        return render(request, 'reports/customer_relationship_analysis.html', context)
    except Exception as e:
        logger.error(f"Error in customer_relationship_analysis: {e}")
        return render(request, 'reports/error.html', {'error': 'An error occurred while generating the customer relationship analysis.'})
