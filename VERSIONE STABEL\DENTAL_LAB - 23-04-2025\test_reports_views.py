#!/usr/bin/env python
"""
Test script to verify reports views are working correctly.
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'LAB.settings')
django.setup()

from django.test import Client
from django.contrib.auth import get_user_model
from django.urls import reverse

def test_reports_views():
    """Test reports views to ensure they render without errors."""

    # Create a test client
    client = Client()

    # Get or create a test user
    User = get_user_model()
    try:
        user = User.objects.get(email='<EMAIL>')
    except User.DoesNotExist:
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )

    # Login the test user
    client.login(email='<EMAIL>', password='testpass123')

    # List of new report URLs to test
    new_reports = [
        'reports_dashboard',
        'profitability_analysis_report',
        'cash_flow_forecast_report',
        'quality_metrics_dashboard',
        'workflow_efficiency_analysis',
        'inventory_optimization_report',
        'demand_forecasting_report',
        'customer_relationship_analysis',
    ]

    print("Testing New Reports Views...")
    print("=" * 60)

    working_views = []
    broken_views = []

    for url_name in new_reports:
        try:
            url = reverse(url_name)
            response = client.get(url)

            if response.status_code == 200:
                working_views.append((url_name, url, response.status_code))
                print(f"✅ {url_name:<35} -> {response.status_code} OK")
            elif response.status_code == 302:
                working_views.append((url_name, url, response.status_code))
                print(f"🔄 {url_name:<35} -> {response.status_code} REDIRECT")
            else:
                broken_views.append((url_name, url, response.status_code))
                print(f"❌ {url_name:<35} -> {response.status_code} ERROR")

        except Exception as e:
            broken_views.append((url_name, 'N/A', str(e)))
            print(f"💥 {url_name:<35} -> EXCEPTION: {e}")

    print("\n" + "=" * 60)
    print(f"SUMMARY:")
    print(f"✅ Working Views: {len(working_views)}")
    print(f"❌ Broken Views:  {len(broken_views)}")

    if broken_views:
        print("\nBROKEN VIEWS:")
        for url_name, url, error in broken_views:
            print(f"  - {url_name}: {error}")
        return False
    else:
        print("\n🎉 All new report views are working correctly!")

        # Test one view in detail
        print("\nTesting Enhanced Dashboard in detail...")
        response = client.get(reverse('reports_dashboard'))
        if response.status_code == 200:
            print(f"✅ Dashboard loaded successfully")
            print(f"   Content length: {len(response.content)} bytes")
            if b'Enhanced Executive Dashboard' in response.content:
                print(f"✅ Dashboard contains expected title")
            else:
                print(f"⚠️  Dashboard title not found in content")

        return True

if __name__ == "__main__":
    success = test_reports_views()
    sys.exit(0 if success else 1)
