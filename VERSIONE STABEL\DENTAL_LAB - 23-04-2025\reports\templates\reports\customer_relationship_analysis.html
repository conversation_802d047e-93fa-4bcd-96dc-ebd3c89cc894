{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}Customer Relationship Analysis{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .customer-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    .customer-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .customer-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .customer-item {
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
        border-left: 4px solid;
    }
    .customer-high-value {
        background-color: #d4edda;
        border-left-color: #28a745;
        color: #155724;
    }
    .customer-medium-value {
        background-color: #d1ecf1;
        border-left-color: #17a2b8;
        color: #0c5460;
    }
    .customer-low-value {
        background-color: #f8f9fa;
        border-left-color: #6c757d;
        color: #495057;
    }
    .risk-high {
        background-color: #f8d7da;
        border-left-color: #dc3545;
        color: #721c24;
    }
    .risk-medium {
        background-color: #fff3cd;
        border-left-color: #ffc107;
        color: #856404;
    }
    .growth-high {
        background-color: #d4edda;
        border-left-color: #28a745;
        color: #155724;
    }
    .clv-chart {
        max-height: 300px;
        overflow-y: auto;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-users"></i> Customer Relationship Analysis
            </h1>
            
            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Analysis Period Start</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">Analysis Period End</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-filter"></i> Analyze Period
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer KPIs -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="customer-card">
                <div class="customer-value">{{ total_customers }}</div>
                <div class="customer-label">Total Customers</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="customer-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="customer-value">{{ retention_rate|floatformat:1 }}%</div>
                <div class="customer-label">Retention Rate</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="customer-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="customer-value">{{ revenue_concentration|floatformat:1 }}%</div>
                <div class="customer-label">Top 10 Revenue Concentration</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="customer-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="customer-value">{{ high_growth_customers|length }}</div>
                <div class="customer-label">High Growth Opportunities</div>
            </div>
        </div>
    </div>

    <!-- Customer Segmentation -->
    <div class="row">
        <!-- Customer Value Segments -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-gem"></i> Customer Value Segments</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6>High Value (>€5,000 CLV)</h6>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-success" style="width: {{ high_value_customers|length|mul:100|div:total_customers }}%"></div>
                        </div>
                        <small>{{ high_value_customers|length }} customers</small>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Medium Value (€1,000-€5,000 CLV)</h6>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-info" style="width: {{ medium_value_customers|length|mul:100|div:total_customers }}%"></div>
                        </div>
                        <small>{{ medium_value_customers|length }} customers</small>
                    </div>
                    
                    <div class="mb-3">
                        <h6>Low Value (<€1,000 CLV)</h6>
                        <div class="progress mb-2">
                            <div class="progress-bar bg-secondary" style="width: {{ low_value_customers|length|mul:100|div:total_customers }}%"></div>
                        </div>
                        <small>{{ low_value_customers|length }} customers</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Risk Analysis -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> Risk Analysis</h5>
                </div>
                <div class="card-body clv-chart">
                    {% for customer in high_risk_customers %}
                    <div class="customer-item risk-high">
                        <div class="d-flex justify-content-between">
                            <div>
                                <strong>{{ customer.dentist_name }}</strong><br>
                                <small>{{ customer.clinic_name }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-danger">High Risk</span><br>
                                <small>€{{ customer.clv|floatformat:0|intcomma }} CLV</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% for customer in medium_risk_customers|slice:":5" %}
                    <div class="customer-item risk-medium">
                        <div class="d-flex justify-content-between">
                            <div>
                                <strong>{{ customer.dentist_name }}</strong><br>
                                <small>{{ customer.clinic_name }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-warning">Medium Risk</span><br>
                                <small>€{{ customer.clv|floatformat:0|intcomma }} CLV</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if not high_risk_customers and not medium_risk_customers %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> No high-risk customers identified!
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Growth Opportunities -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-line"></i> Growth Opportunities</h5>
                </div>
                <div class="card-body clv-chart">
                    {% for customer in high_growth_customers %}
                    <div class="customer-item growth-high">
                        <div class="d-flex justify-content-between">
                            <div>
                                <strong>{{ customer.dentist_name }}</strong><br>
                                <small>{{ customer.clinic_name }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-success">High Potential</span><br>
                                <small>{{ customer.case_frequency|floatformat:1 }} cases/month</small>
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> No high-growth opportunities identified in current period.
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Lifetime Value Chart -->
    <div class="row mt-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-bar"></i> Top 15 Customers by Lifetime Value</h5>
                </div>
                <div class="card-body">
                    <canvas id="clvChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Customer Insights -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> Customer Insights</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Key Insights</h6>
                        <ul class="mb-0">
                            <li>{{ high_value_customers|length }} high-value customers</li>
                            <li>{{ retention_rate|floatformat:1 }}% retention rate</li>
                            <li>{{ revenue_concentration|floatformat:1 }}% revenue from top 10</li>
                            <li>{{ high_risk_customers|length }} customers at risk</li>
                        </ul>
                    </div>
                    
                    {% if retention_rate < 80 %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Action Required</h6>
                        <p class="mb-0">Retention rate below 80%. Focus on customer satisfaction and engagement.</p>
                    </div>
                    {% endif %}
                    
                    <div class="alert alert-success">
                        <h6><i class="fas fa-target"></i> Recommendations</h6>
                        <ul class="mb-0">
                            <li>Nurture high-value relationships</li>
                            <li>Address at-risk customers</li>
                            <li>Develop growth opportunities</li>
                            <li>Improve payment processes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Customer Analysis -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> Detailed Customer Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Clinic</th>
                                    <th>Cases</th>
                                    <th>Revenue</th>
                                    <th>CLV</th>
                                    <th>Frequency</th>
                                    <th>Payment Score</th>
                                    <th>Risk Level</th>
                                    <th>Growth Potential</th>
                                    <th>Last Activity</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for customer in customer_analysis|slice:":20" %}
                                <tr>
                                    <td><strong>{{ customer.dentist_name }}</strong></td>
                                    <td>{{ customer.clinic_name|truncatechars:20 }}</td>
                                    <td>{{ customer.total_cases }}</td>
                                    <td>€{{ customer.total_revenue|floatformat:0|intcomma }}</td>
                                    <td>€{{ customer.clv|floatformat:0|intcomma }}</td>
                                    <td>{{ customer.case_frequency|floatformat:1 }}/mo</td>
                                    <td>
                                        <span class="badge 
                                            {% if customer.payment_score >= 80 %}bg-success
                                            {% elif customer.payment_score >= 60 %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ customer.payment_score|floatformat:0 }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if customer.risk_level == 'Low' %}bg-success
                                            {% elif customer.risk_level == 'Medium' %}bg-warning
                                            {% else %}bg-danger{% endif %}">
                                            {{ customer.risk_level }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if customer.growth_potential == 'High' %}bg-success
                                            {% elif customer.growth_potential == 'Medium' %}bg-info
                                            {% else %}bg-secondary{% endif %}">
                                            {{ customer.growth_potential }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if customer.last_case_date %}
                                        {{ customer.last_case_date|date:"M d, Y" }}
                                        {% else %}
                                        <span class="text-muted">N/A</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="10" class="text-center text-muted">No customer data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6>Export Options</h6>
                    <a href="#" class="btn btn-success me-2" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> Export Analysis to Excel
                    </a>
                    <a href="#" class="btn btn-danger" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> Export Report to PDF
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
// Customer Lifetime Value Chart
const clvCtx = document.getElementById('clvChart').getContext('2d');
const clvChart = new Chart(clvCtx, {
    type: 'bar',
    data: {
        labels: [
            {% for customer in customer_analysis|slice:":15" %}
            '{{ customer.dentist_name|truncatechars:15 }}'{% if not forloop.last %},{% endif %}
            {% endfor %}
        ],
        datasets: [{
            label: 'Customer Lifetime Value (€)',
            data: [
                {% for customer in customer_analysis|slice:":15" %}
                {{ customer.clv|default:0 }}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            backgroundColor: [
                {% for customer in customer_analysis|slice:":15" %}
                {% if customer.clv > 5000 %}'rgba(40, 167, 69, 0.8)'
                {% elif customer.clv > 1000 %}'rgba(23, 162, 184, 0.8)'
                {% else %}'rgba(108, 117, 125, 0.8)'{% endif %}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderColor: [
                {% for customer in customer_analysis|slice:":15" %}
                {% if customer.clv > 5000 %}'rgba(40, 167, 69, 1)'
                {% elif customer.clv > 1000 %}'rgba(23, 162, 184, 1)'
                {% else %}'rgba(108, 117, 125, 1)'{% endif %}{% if not forloop.last %},{% endif %}
                {% endfor %}
            ],
            borderWidth: 1
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                title: {
                    display: true,
                    text: 'Customer Lifetime Value (€)'
                }
            },
            x: {
                title: {
                    display: true,
                    text: 'Customers'
                }
            }
        },
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                callbacks: {
                    afterLabel: function(context) {
                        const customer = {{ customer_analysis|slice:":15"|safe }};
                        if (customer[context.dataIndex]) {
                            return [
                                'Cases: ' + customer[context.dataIndex].total_cases,
                                'Revenue: €' + customer[context.dataIndex].total_revenue.toLocaleString(),
                                'Risk: ' + customer[context.dataIndex].risk_level
                            ];
                        }
                        return [];
                    }
                }
            }
        }
    }
});

function exportToExcel() {
    alert('Excel export functionality will be implemented');
}

function exportToPDF() {
    alert('PDF export functionality will be implemented');
}
</script>
{% endblock %}
