{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block title %}Inventory Optimization Report{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.css">
<style>
    .inventory-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        text-align: center;
    }
    .inventory-value {
        font-size: 2rem;
        font-weight: bold;
    }
    .inventory-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
    .abc-class-a {
        background-color: #d4edda;
        border-left: 4px solid #28a745;
        color: #155724;
    }
    .abc-class-b {
        background-color: #d1ecf1;
        border-left: 4px solid #17a2b8;
        color: #0c5460;
    }
    .abc-class-c {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
        color: #721c24;
    }
    .optimization-item {
        padding: 15px;
        margin-bottom: 10px;
        border-radius: 8px;
    }
    .optimization-excess {
        background-color: #fff3cd;
        border-left: 4px solid #ffc107;
        color: #856404;
    }
    .optimization-stockout {
        background-color: #f8d7da;
        border-left: 4px solid #dc3545;
        color: #721c24;
    }
    .eoq-recommendation {
        background: #e3f2fd;
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="h3 mb-4">
                <i class="fas fa-boxes"></i> Inventory Optimization Report
            </h1>
            
            <!-- Date Range Filter -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Analysis Period Start</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ start_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">Analysis Period End</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ end_date|date:'Y-m-d' }}">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary d-block">
                                <i class="fas fa-filter"></i> Analyze Period
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory KPIs -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="inventory-card">
                <div class="inventory-value">€{{ total_inventory_value|floatformat:0|intcomma }}</div>
                <div class="inventory-label">Total Inventory Value</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="inventory-card" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);">
                <div class="inventory-value">€{{ total_holding_cost|floatformat:0|intcomma }}</div>
                <div class="inventory-label">Annual Holding Cost</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="inventory-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="inventory-value">{{ class_a_items|length }}</div>
                <div class="inventory-label">Class A Items (High Value)</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="inventory-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="inventory-value">{{ optimization_opportunities|length }}</div>
                <div class="inventory-label">Optimization Opportunities</div>
            </div>
        </div>
    </div>

    <!-- ABC Analysis and Optimization Opportunities -->
    <div class="row">
        <!-- ABC Classification -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-chart-pie"></i> ABC Classification Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-4 text-center">
                            <div class="abc-class-a p-3 rounded">
                                <h4>{{ class_a_items|length }}</h4>
                                <small>Class A Items<br>(80% of value)</small>
                            </div>
                        </div>
                        <div class="col-4 text-center">
                            <div class="abc-class-b p-3 rounded">
                                <h4>{{ class_b_items|length }}</h4>
                                <small>Class B Items<br>(15% of value)</small>
                            </div>
                        </div>
                        <div class="col-4 text-center">
                            <div class="abc-class-c p-3 rounded">
                                <h4>{{ class_c_items|length }}</h4>
                                <small>Class C Items<br>(5% of value)</small>
                            </div>
                        </div>
                    </div>
                    <canvas id="abcChart" height="200"></canvas>
                </div>
            </div>
        </div>
        
        <!-- Optimization Opportunities -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-exclamation-triangle"></i> Optimization Opportunities</h5>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% for opportunity in optimization_opportunities %}
                    <div class="optimization-item 
                        {% if opportunity.type == 'Excess Stock' %}optimization-excess
                        {% elif opportunity.type == 'Stockout Risk' %}optimization-stockout{% endif %}">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ opportunity.type }}</h6>
                                <strong>{{ opportunity.item }}</strong><br>
                                <small>Current: {{ opportunity.current_stock }} | Recommended: {{ opportunity.recommended_stock }}</small>
                            </div>
                            <div class="text-end">
                                {% if opportunity.potential_savings %}
                                <strong>€{{ opportunity.potential_savings|floatformat:0 }}</strong><br>
                                <small>potential savings</small>
                                {% elif opportunity.urgency %}
                                <span class="badge bg-danger">{{ opportunity.urgency }}</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> No immediate optimization opportunities identified!
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Inventory Analysis -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-table"></i> Detailed Inventory Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>Item</th>
                                    <th>ABC Class</th>
                                    <th>Current Stock</th>
                                    <th>Usage (Period)</th>
                                    <th>Annual Forecast</th>
                                    <th>EOQ</th>
                                    <th>Reorder Point</th>
                                    <th>Seasonality</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in inventory_analysis %}
                                <tr>
                                    <td><strong>{{ item.item_name }}</strong></td>
                                    <td>
                                        <span class="badge 
                                            {% if item.abc_class == 'A' %}bg-success
                                            {% elif item.abc_class == 'B' %}bg-info
                                            {% else %}bg-secondary{% endif %}">
                                            {{ item.abc_class }}
                                        </span>
                                    </td>
                                    <td>{{ item.current_stock }}</td>
                                    <td>{{ item.total_usage }}</td>
                                    <td>{{ item.annual_usage_forecast|floatformat:0 }}</td>
                                    <td>{{ item.eoq|floatformat:0 }}</td>
                                    <td>{{ item.reorder_point|floatformat:0 }}</td>
                                    <td>
                                        {% if item.seasonality_index > 0.5 %}
                                        <span class="badge bg-warning">High</span>
                                        {% elif item.seasonality_index > 0.2 %}
                                        <span class="badge bg-info">Medium</span>
                                        {% else %}
                                        <span class="badge bg-success">Low</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if item.stockout_risk == 'High' %}
                                        <span class="badge bg-danger">Low Stock</span>
                                        {% elif item.excess_stock > 0 %}
                                        <span class="badge bg-warning">Excess</span>
                                        {% else %}
                                        <span class="badge bg-success">Optimal</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="9" class="text-center text-muted">No inventory data available</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- EOQ Recommendations and Seasonal Analysis -->
    <div class="row mt-4">
        <!-- EOQ Recommendations -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-calculator"></i> EOQ Recommendations</h5>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    {% for item in class_a_items|slice:":10" %}
                    <div class="eoq-recommendation">
                        <div class="d-flex justify-content-between">
                            <div>
                                <strong>{{ item.item_name }}</strong><br>
                                <small>Current Order: {{ item.current_stock }} | EOQ: {{ item.eoq|floatformat:0 }}</small>
                            </div>
                            <div class="text-end">
                                {% if item.eoq > item.current_stock %}
                                <span class="badge bg-info">Increase Order</span>
                                {% elif item.eoq < item.current_stock %}
                                <span class="badge bg-warning">Reduce Order</span>
                                {% else %}
                                <span class="badge bg-success">Optimal</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-muted">No Class A items for EOQ analysis.</p>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <!-- Recommendations -->
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5><i class="fas fa-lightbulb"></i> Optimization Recommendations</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> Key Actions</h6>
                        <ul class="mb-0">
                            <li>Focus on Class A items for tight control</li>
                            <li>Implement EOQ for high-value items</li>
                            <li>Review reorder points quarterly</li>
                            <li>Monitor seasonal patterns</li>
                        </ul>
                    </div>
                    
                    {% if optimization_opportunities %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle"></i> Immediate Actions</h6>
                        <ul class="mb-0">
                            {% for opportunity in optimization_opportunities|slice:":3" %}
                            <li>{{ opportunity.type }}: {{ opportunity.item }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    <div class="alert alert-success">
                        <h6><i class="fas fa-target"></i> Optimization Targets</h6>
                        <ul class="mb-0">
                            <li>Reduce holding costs by 15%</li>
                            <li>Maintain 95% service level</li>
                            <li>Minimize stockouts to <2%</li>
                            <li>Optimize inventory turnover</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h6>Export Options</h6>
                    <a href="#" class="btn btn-success me-2" onclick="exportToExcel()">
                        <i class="fas fa-file-excel"></i> Export Analysis to Excel
                    </a>
                    <a href="#" class="btn btn-danger" onclick="exportToPDF()">
                        <i class="fas fa-file-pdf"></i> Export Report to PDF
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
<script>
// ABC Classification Chart
const abcCtx = document.getElementById('abcChart').getContext('2d');
const abcChart = new Chart(abcCtx, {
    type: 'doughnut',
    data: {
        labels: ['Class A (High Value)', 'Class B (Medium Value)', 'Class C (Low Value)'],
        datasets: [{
            data: [{{ class_a_items|length }}, {{ class_b_items|length }}, {{ class_c_items|length }}],
            backgroundColor: [
                'rgba(40, 167, 69, 0.8)',
                'rgba(23, 162, 184, 0.8)',
                'rgba(220, 53, 69, 0.8)'
            ],
            borderColor: [
                'rgba(40, 167, 69, 1)',
                'rgba(23, 162, 184, 1)',
                'rgba(220, 53, 69, 1)'
            ],
            borderWidth: 2
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            },
            tooltip: {
                callbacks: {
                    label: function(context) {
                        const total = {{ class_a_items|length|add:class_b_items|length|add:class_c_items|length }};
                        const percentage = ((context.parsed / total) * 100).toFixed(1);
                        return context.label + ': ' + context.parsed + ' items (' + percentage + '%)';
                    }
                }
            }
        }
    }
});

function exportToExcel() {
    // Implementation for Excel export
    alert('Excel export functionality will be implemented');
}

function exportToPDF() {
    // Implementation for PDF export
    alert('PDF export functionality will be implemented');
}
</script>
{% endblock %}
